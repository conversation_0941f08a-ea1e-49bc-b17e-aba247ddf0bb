# Facility WMS Integration Safety Feature

## Overview

This safety feature prevents accidental order creation in production WMS warehouses by adding a facility-level control for WMS integration access.

## Problem Solved

- **Production Safety**: Prevents accidental order creation in live production WMS systems
- **Granular Control**: Enable/disable WMS integration per facility
- **Fail Fast**: Immediate validation at API level instead of failing in background tasks

## How It Works

### 1. Database Schema

Added `wms_integration_enabled` field to the `Facility` model:

```python
class Facility(BaseModel):
    name = models.Char<PERSON>ield(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    wms_integration_enabled = models.BooleanField(
        default=False,  # Safe default - disabled by default
        help_text="Enable WMS order creation for this facility. Use with caution in production."
    )
```

### 2. Validation Logic

**API Level Validation** (`/integrations/wms/order/create/`):

- Fetches order data from OMS to get facility name
- Checks if facility exists and has WMS integration enabled
- Returns immediate error if disabled (HTTP 403 Forbidden)
- Only queues Celery task if validation passes

**Method**: `Facility.can_create_wms_orders()`

- Returns `True` only if facility is both active AND WMS integration enabled
- Provides clear safety check

### 3. Management Commands

Use the management command to control facility WMS access:

```bash
# List all facilities and their WMS status
python manage.py manage_facility_wms list

# Enable WMS integration for specific facility
python manage.py manage_facility_wms enable --facility ROZANA_TEST_WH1

# Disable WMS integration for specific facility  
python manage.py manage_facility_wms disable --facility ROZANA_TEST_WH1

# Enable for all facilities (use with caution!)
python manage.py manage_facility_wms enable --all

# Disable for all facilities (safe default)
python manage.py manage_facility_wms disable --all
```

## API Response Examples

### ✅ Success (WMS Enabled)

```json
{
  "success": true,
  "order_id": "POS41904046409_test1",
  "task_id": "abc123-def456-789",
  "message": "Order sync to Liink triggered successfully"
}
```

### ❌ WMS Integration Disabled

```json
{
  "success": false,
  "error": "WMS integration disabled",
  "message": "WMS order creation is disabled for facility: ROZANA_TEST_WH1"
}
```

### ❌ Facility Not Found

```json
{
  "success": false,
  "error": "Order not found", 
  "message": "Order ORDER123 not found in OMS database"
}
```

## Production Deployment Checklist

1. **Deploy Code**: Deploy the updated Potions service with facility validation
2. **Run Migration**: Apply the database migration to add the new field
3. **Review Facilities**: List all facilities and their current status
4. **Enable Selectively**: Only enable WMS integration for facilities that should create orders
5. **Test**: Verify that disabled facilities properly reject order creation
6. **Monitor**: Watch logs for any blocked order attempts

## Safety Benefits

- **Default Safe**: New facilities have WMS integration disabled by default
- **Explicit Enable**: Must explicitly enable WMS integration per facility
- **Immediate Feedback**: API returns error immediately instead of failing async task
- **Resource Efficient**: Doesn't waste Celery worker time on doomed tasks
- **Audit Trail**: Clear logging of blocked attempts

## Example Usage

```python
# Check if facility can create WMS orders
facility = Facility.objects.get(name='ROZANA_TEST_WH1')
if facility.can_create_wms_orders():
    # Safe to create WMS orders
    pass
else:
    # WMS integration disabled - block order creation
    pass
```

This feature provides a crucial safety net for production environments where accidental WMS order creation could have serious consequences.
