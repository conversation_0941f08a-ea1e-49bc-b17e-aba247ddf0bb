# Invoice Callback API

This API handles invoice creation callbacks, fetches invoice HTML from StockOne, converts it to PDF, and uploads to S3.

## Overview

The workflow is:
1. Receive invoice callback with `order_status: "invoiced"`
2. Validate the callback data
3. Trigger asynchronous processing (Celery task)
4. Fetch invoice HTML from StockOne API
5. Convert HTML to PDF using WeasyPrint
6. Upload PDF to S3 with organized folder structure
7. Return S3 URL

## API Endpoints

### 1. Invoice Callback
**POST** `/api/potions/integrations/invoice/callback/`

Receives invoice creation callbacks and starts processing.

#### Request Body
```json
{
  "data": {
    "order_reference": "ORDgdfdffs5",
    "order_status": "invoiced",
    "order_date": "2025-08-11 13:54:57",
    "customer_name": "Ravi Ranjan",
    "invoice_amount": 231.28,
    "order_id": "MN3193",
    "shipment_date": "2025-08-11 13:54:57",
    // ... other order data
  },
  "warehouse": "ROZANA_TEST_WH1",
  "wms_integration": "default"  // Optional: specify WMS integration name
}
```

#### Response (202 Accepted)
```json
{
  "message": "Invoice callback received and processing started",
  "order_reference": "ORDgdfdffs5",
  "warehouse": "ROZANA_TEST_WH1",
  "task_id": "celery-task-id-here",
  "status": "processing_started"
}
```

#### Response (200 OK - Non-invoiced status)
```json
{
  "message": "Order status is pending, not invoiced. Ignoring callback.",
  "order_reference": "ORDgdfdffs5",
  "status": "pending"
}
```

### 2. Task Status Check
**GET** `/api/potions/integrations/invoice/status/{task_id}/`

Check the status of invoice processing task.

#### Response (In Progress)
```json
{
  "task_id": "celery-task-id-here",
  "status": "PENDING"
}
```

#### Response (Success)
```json
{
  "task_id": "celery-task-id-here",
  "status": "SUCCESS",
  "order_reference": "ORDgdfdffs5",
  "warehouse": "ROZANA_TEST_WH1",
  "pdf_url": "https://rozana-invoices.s3.ap-south-1.amazonaws.com/invoices/ROZANA_TEST_WH1/2025/08/ORDgdfdffs5.pdf"
}
```

#### Response (Failed)
```json
{
  "task_id": "celery-task-id-here",
  "status": "FAILURE",
  "error": "Error message here"
}
```

## Configuration

### Database Configuration (Recommended)

Create WMS integration records in the database using Django admin:

1. Go to Django Admin → Integrations → WMS Integrations
2. Create a new WMS Integration with:
   - **Name**: `default` (or custom name)
   - **Base URL**: `https://rzn1-be.stockone.com`
   - **Client ID**: Your OAuth2 client ID
   - **Client Secret**: Your OAuth2 client secret
   - **Is Active**: ✓

### Environment Variables

Add these environment variables to your `.env` file:

```bash
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_REGION_NAME=ap-south-1
AWS_STORAGE_BUCKET_NAME=rozana-invoices

# WMS OAuth2 Configuration (preferred)
STOCKONE_BASE_URL=https://rzn1-be.stockone.com
STOCKONE_CLIENT_ID=your_oauth2_client_id
STOCKONE_CLIENT_SECRET=your_oauth2_client_secret

# Legacy Token Authentication (fallback)
STOCKONE_AUTH_TOKEN=JqvBnEejVQCJYNDEDM2dVMx2L5IcIp
STOCKONE_COMPANY_CODE=4
```

### Authentication Priority

1. **Database WMS Integration** (highest priority)
2. **Environment OAuth2 credentials** (fallback)
3. **Legacy token authentication** (last resort)

## S3 Folder Structure

PDFs are organized in S3 with the following structure:
```
invoices/
├── WAREHOUSE_NAME/
│   ├── YEAR/
│   │   ├── MONTH/
│   │   │   ├── ORDER_REFERENCE_1.pdf
│   │   │   ├── ORDER_REFERENCE_2.pdf
│   │   │   └── ...
```

Example: `invoices/ROZANA_TEST_WH1/2025/08/ORDgdfdffs5.pdf`

## Dependencies

The following packages are required:
- `weasyprint==62.3` - HTML to PDF conversion
- `boto3` - AWS S3 integration
- `celery` - Asynchronous task processing
- `requests` - HTTP requests to StockOne API

## Testing

Use the provided test script:

```bash
cd /path/to/potions/integrations/
python test_invoice_callback.py http://localhost:8000
```

## Error Handling

- **Invalid callback data**: Returns 400 Bad Request
- **Non-invoiced status**: Returns 200 OK with message (no processing)
- **StockOne API errors**: Task fails with retry (max 3 retries)
- **PDF conversion errors**: Task fails with retry
- **S3 upload errors**: Task fails with retry

## Asynchronous Processing

The API uses Celery for asynchronous processing:

1. **Immediate response**: API returns 202 Accepted with task ID
2. **Background processing**: Celery worker processes the invoice
3. **Status tracking**: Use task ID to check progress
4. **Retry mechanism**: Failed tasks are retried up to 3 times

## StockOne API Integration

The system calls StockOne's print invoice API:

```bash
GET /api/v1/outbound/print_invoice/?order_reference=ORDER_REF&external_call=true&order_type=normal
Headers:
- authorization: {STOCKONE_AUTH_TOKEN}
- company-code: {STOCKONE_COMPANY_CODE}
- warehouse: {WAREHOUSE_NAME}
```

## Security

- OAuth2 authentication middleware applies to all `/api/potions/integrations/` endpoints
- S3 uploads include metadata for tracking
- Error messages are logged but not exposed in API responses

## Monitoring

- All operations are logged with appropriate log levels
- Celery task results can be monitored via Flower or Django admin
- S3 upload metadata includes timestamps and source information
