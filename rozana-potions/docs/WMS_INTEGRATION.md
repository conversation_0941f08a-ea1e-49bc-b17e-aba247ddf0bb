# WMS Integration with Liink API

This document describes the WMS integration architecture change where Potions service now handles order synchronization with Liink API instead of OMS directly calling WMS.

## Architecture Overview

```
OMS Order Created → POST /integrations/order/create/ {"order_id": "POS123"}
                 → Celery Task: sync_order_to_liink_task.apply_async(order_id)
                 → Fetch order data from OMS DB
                 → Transform to Liink format
                 → POST to Liink API
                 → Update OMS order status (SUCCESS/FAILED)
```

## API Endpoints

### 1. Create Order
**Endpoint:** `POST /integrations/order/create/`

**Request:**
```json
{
    "order_id": "POS41904046409_test1"
}
```

**Response:**
```json
{
    "success": true,
    "order_id": "POS41904046409_test1",
    "task_id": "celery-task-id",
    "message": "Order sync to Liink triggered successfully"
}
```

### 2. Cancel Order
**Endpoint:** `POST /integrations/order/cancel/`

**Request:**
```json
{
    "order_id": "POS41904046409_test1"
}
```

**Response:**
```json
{
    "success": true,
    "order_id": "POS41904046409_test1",
    "task_id": "celery-task-id",
    "message": "Order cancellation in Liink triggered successfully"
}
```

## Configuration

Add the following environment variables to your `.env` file:

```env
# Liink API Configuration
LIINK_BASE_URL=https://liink-backend.stockone.com
LIINK_AUTH_TOKEN=cb02c9ea3ab65d850f75155651d4fc7442b5f5bd
LIINK_WORKSPACE=ROZANA
```

## Data Transformation

The service transforms OMS order data to Liink API format:

### OMS Order Data → Liink Payload

- **Order ID** → `order_reference`
- **Customer Info** → `customer` object
- **Order Items** → `items` array with `pack_uom_quantity` in `aux_data`
- **Facility Name** → `warehouse`
- **Time Slots** → `slot_from`, `slot_to`, `promised_time`

### Sample Liink Payload

```json
{
  "order_type": "POS_EXPRESS",
  "slot_from": "11:39:00",
  "slot_to": "17:12:00",
  "promised_time": "2025-07-18 17:12:00",
  "warehouse": "hr009_pla_ls1",
  "customer": {
    "customer_reference": "896226",
    "customer_name": "Ravi",
    "address_line_1": "Store,Palla,121003",
    "state": "Haryana",
    "city": "26",
    "pincode": "121003",
    "ship_group": "278",
    "address_line_2": "278"
  },
  "order_reference": "POS41904046409_test1",
  "items": [
    {
      "aux_data": {
        "pack_uom_quantity": 1
      },
      "sku": "ROZ3615-1PCS",
      "line_reference": "468205",
      "quantity": 1,
      "mrp": 40,
      "discount_amount": 20.2,
      "sale_price_gst": 19.8
    }
  ],
  "auto_pick_and_invoice": "true"
}
```

## Order Status Updates

The service updates OMS order status based on Liink API response:

- **Success:** Status = `21` (WMS_SYNCED)
- **Failure:** Status = `22` (WMS_SYNC_FAILED)

## Error Handling

- **Retry Logic:** Tasks retry up to 3 times with exponential backoff
- **Database Errors:** Proper error logging and status updates
- **API Errors:** Detailed error messages and status tracking
- **Timeout Handling:** 60-second timeout for API calls

## Monitoring

- **Celery Tasks:** Monitor task status via Celery monitoring tools
- **Logs:** Comprehensive logging for debugging and monitoring
- **Task IDs:** Returned in API response for tracking

## Testing

### Test Order Creation
```bash
curl -X POST http://localhost:8000/integrations/order/create/ \
  -H "Content-Type: application/json" \
  -d '{"order_id": "POS41904046409_test1"}'
```

### Test Order Cancellation
```bash
curl -X POST http://localhost:8000/integrations/order/cancel/ \
  -H "Content-Type: application/json" \
  -d '{"order_id": "POS41904046409_test1"}'
```

## Deployment

1. Update environment variables
2. Run migrations (if any)
3. Restart Celery workers
4. Restart Django application
5. Update OMS to call new Potions endpoints

## Migration from OMS WMS Integration

1. **Remove WMS integration** from OMS service
2. **Update OMS order creation** to call Potions `/integrations/order/create/`
3. **Configure Liink credentials** in Potions environment
4. **Test integration** with sample orders
5. **Monitor logs** for successful migration
