# WMS Return Integration Documentation

## Overview

This document explains the order return flow implementation and how it integrates with TMS, OMS, and WMS systems in the Rozana Potions project.

## System Architecture

### Components
- **OMS (Order Management System)**: Central system managing order lifecycle and return data
- **TMS (Transportation Management System)**: Handles logistics and shipping (Shipsy)
- **WMS (Warehouse Management System)**: Manages warehouse operations (StockOne/Liink)

## Order Return Flow

### 1. Return Initiation
```
Customer Request → OMS → Return Record Created
```
- Customer initiates return request
- O<PERSON> creates return record with unique `return_reference`
- Return details stored in `returns` and `return_items` tables

### 2. Reverse Logistics (TMS)
```
OMS → TMS → Reverse Consignment → Pickup Arranged
```
- OMS triggers reverse consignment creation in TMS
- TMS arranges pickup from customer location
- TMS provides tracking and status updates via webhooks

### 3. Return Delivery to Warehouse
```
TMS Webhook → OMS → WMS Notification
```
- TMS sends webhook when return is delivered to warehouse
- OMS processes webhook and triggers WMS notification
- WMS processes return and updates inventory

## Implementation Details

### Core Files Created/Modified

1. **`integrations/services/wms/return_service.py`**
   - Main WMS return service implementation
   - Handles return data fetching, WMS API calls, and status updates

2. **`integrations/services/wms/wms_service.py`**
   - Unified WMS service interface
   - Exports `process_order_return_to_wms` function

3. **`integrations/tasks/wms_orders.py`**
   - Added `process_return_to_wms_task` Celery task
   - Asynchronous processing with retry logic

4. **`events/handlers.py`**
   - Added `handle_order_return_event` function
   - Event-driven return processing

5. **`events/registry.py`**
   - Registered return event handler

### Key Functions

#### `process_order_return_to_wms(order_id, return_details)`
Main function to process order returns to WMS.

**Parameters:**
- `order_id`: Original order ID
- `return_details`: Dictionary containing:
  - `return_reference`: Unique return identifier (required)
  - `return_reason`: Reason for return
  - `warehouse`: Target warehouse
  - `items`: List of returned items (optional)

**Returns:**
- Dictionary with processing result and status

#### `handle_order_return_event(order_id, return_details)`
Event handler for return processing (called from TMS webhooks).

**Usage:**
```python
# Synchronous processing
from integrations.services.wms.wms_service import process_order_return_to_wms
result = process_order_return_to_wms(order_id, return_details)

# Asynchronous processing (recommended)
from integrations.tasks.wms_orders import process_return_to_wms_task
task = process_return_to_wms_task.apply_async(args=[order_id, return_details])
```

## Database Schema

### Returns Table
```sql
returns:
- id (primary key)
- return_reference (unique)
- order_id (foreign key to orders)
- return_reason
- return_status
- wms_return_id (WMS reference)
- created_at
- updated_at
```

### Return Items Table
```sql
return_items:
- id (primary key)
- return_id (foreign key to returns)
- order_item_id (foreign key to order_items)
- sku
- product_name
- quantity
- unit_price
- return_reason
```

## WMS API Integration

### Endpoint: `/api/v2/inbound/returns/`
**Method:** POST

**Payload Structure:**
```json
{
  "return_reference": "RET123456",
  "original_order_id": "ORD789",
  "customer_name": "John Doe",
  "return_reason": "Customer Return",
  "facility_id": "hr009_pla_ls1",
  "return_type": "customer_return",
  "expected_return_date": "2024-01-15T10:00:00Z",
  "items": [
    {
      "sku": "PROD001",
      "product_name": "Product Name",
      "quantity": 2,
      "unit_price": 100.0,
      "return_reason": "Defective",
      "batch_number": "BATCH123",
      "expiry_date": "2024-12-31"
    }
  ],
  "metadata": {
    "source": "OMS",
    "created_by": "potions_integration",
    "return_status": "pending"
  }
}
```

## Integration Points

### TMS Webhook Integration
When TMS sends a webhook indicating return delivery:

```python
# In TMS webhook handler
def handle_tms_return_webhook(webhook_payload):
    if webhook_payload.get('type') == 'return_delivered':
        order_id = webhook_payload.get('customer_reference_number')
        return_details = {
            'return_reference': webhook_payload.get('return_reference'),
            'return_reason': webhook_payload.get('return_reason', 'Customer Return'),
            'warehouse': webhook_payload.get('destination_hub')
        }
        
        # Trigger return processing
        handle_order_return_event(order_id, return_details)
```

### Event-Driven Processing
Using the event system:

```python
from events.publisher import publish_event

# Publish return event
publish_event(
    event_type="order_return",
    event_format="return_delivered",
    payload={
        'order_id': order_id,
        'return_details': return_details
    },
    facility_name=warehouse
)
```

## Error Handling

### Retry Logic
- Celery tasks include automatic retry with exponential backoff
- Maximum 3 retries with 60-second initial delay
- Non-retryable errors (data validation) are handled gracefully

### Status Tracking
Return statuses in OMS:
- `pending`: Initial return created
- `tms_notified`: TMS reverse consignment created
- `in_transit`: Return in transit to warehouse
- `wms_notified`: WMS notified of return
- `wms_failed`: WMS processing failed
- `processed`: Return fully processed

## Testing

### Unit Tests
Test the return service functionality:

```python
from integrations.services.wms.return_service import WMSReturnService

def test_process_return():
    service = WMSReturnService()
    result = service.process_order_return_to_wms(
        order_id="TEST123",
        return_details={
            'return_reference': 'RET123',
            'return_reason': 'Test Return'
        }
    )
    assert result['success'] == True
```

### Integration Tests
Test end-to-end flow:

```python
def test_return_flow():
    # 1. Create return in OMS
    # 2. Trigger TMS webhook
    # 3. Verify WMS notification
    # 4. Check status updates
    pass
```

## Monitoring and Logging

### Log Levels
- `INFO`: Normal processing flow
- `WARNING`: Recoverable errors
- `ERROR`: Processing failures
- `DEBUG`: Detailed debugging information

### Key Metrics
- Return processing success rate
- WMS API response times
- Failed return notifications
- Retry attempts and success rates

## Configuration

### Environment Variables
```bash
# WMS Configuration
WMS_BASE_URL=https://rzn1-be.stockone.com
WMS_CLIENT_ID=your_client_id
WMS_CLIENT_SECRET=your_client_secret

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379
CELERY_RESULT_BACKEND=redis://redis:6379/1
```

### Database Settings
```python
DATABASES = {
    'oms': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'oms',
        'USER': 'postgres',
        'PASSWORD': 'password',
        'HOST': 'db1',
        'PORT': '5432',
    }
}
```

## Next Steps

1. **API Documentation**: Document WMS return API endpoints
2. **Monitoring Dashboard**: Create return processing metrics dashboard
3. **Automated Testing**: Implement comprehensive test suite
4. **Performance Optimization**: Optimize database queries and API calls
5. **Error Alerting**: Set up alerts for failed return processing
