FROM python:3.12

ENV PYTHONUNBUFFERED=1

RUN apt-get -y update && apt-get -y upgrade && apt-get clean

WORKDIR /application/

COPY app/requirements.txt requirements.txt

RUN pip install --upgrade pip

RUN pip install --no-cache-dir -r requirements.txt

COPY app/potions/ /application/

RUN ["chmod", "+x", "/application/docker-entrypoint.sh"]

ENTRYPOINT ["bash" , "/application/docker-entrypoint.sh" ]

CMD ["uvicorn", "potions.asgi:application", "--host", "0.0.0.0", "--port", "8000"]
