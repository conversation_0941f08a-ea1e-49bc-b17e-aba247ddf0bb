from django.contrib import admin
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django_json_widget.widgets import JSONEditorWidget
from integrations.models import WMSIntegration, LambdaIntegration, TMSWebhookEvent

# Register your models here.


@admin.register(TMSWebhookEvent)
class TMSWebhookEventAdmin(admin.ModelAdmin):
    """
    Admin interface for TMS Webhook Events.
    """
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }

    list_display = ['reference_number', 'customer_reference_number', 'hub_code', 'type', 'event_time', 'created_at']
    list_filter = ['type', 'hub_code', 'event_time', 'created_at']
    search_fields = ['reference_number', 'customer_reference_number', 'hub_code']
    readonly_fields = ['reference_number', 'customer_reference_number', 'hub_code', 'type', 'event_time', 'webhook_payload', 'created_at', 'updated_at']
    ordering = ['-event_time']

@admin.register(WMSIntegration)
class WMSIntegrationAdmin(admin.ModelAdmin):
    list_display = ('name', 'base_url', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'client_id', 'base_url')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'base_url', 'is_active')
        }),
        ('Authentication', {
            'fields': ('client_id', 'client_secret'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(LambdaIntegration)
class LambdaIntegrationAdmin(admin.ModelAdmin):
    list_display = ('name', 'lambda_type', 'function_name', 'region', 'is_active', 'created_at', 'updated_at')
    list_filter = ('lambda_type', 'is_active', 'region', 'created_at')
    search_fields = ('name', 'function_name', 'endpoint_url')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'lambda_type', 'is_active')
        }),
        ('Lambda Configuration', {
            'fields': ('function_name', 'region', 'timeout')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
        ('Authentication', {
            'fields': ('aws_access_key_id', 'aws_secret_access_key'),
            'classes': ('collapse',)
        })
    )
