# Generated by Django 5.2.5 on 2025-08-06 14:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LambdaIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('lambda_type', models.CharField(choices=[('redis', 'Redis Lambda'), ('typesense', 'Typesense Lambda')], max_length=20)),
                ('function_name', models.CharField(help_text='AWS Lambda function name', max_length=255)),
                ('region', models.CharField(default='us-east-1', help_text='AWS region', max_length=50)),
                ('timeout', models.IntegerField(default=30, help_text='Request timeout in seconds')),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'OMS_REDIS_INTEGRATION',
            },
        ),
        migrations.CreateModel(
            name='WMSIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('base_url', models.URLField(default='https://rzn1-be.stockone.com', max_length=255)),
                ('client_id', models.CharField(max_length=100)),
                ('client_secret', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'WMS_INTEGRATION',
            },
        ),
    ]
