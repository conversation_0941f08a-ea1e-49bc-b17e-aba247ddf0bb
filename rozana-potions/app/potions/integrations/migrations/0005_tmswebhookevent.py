# Generated by Django 5.2.5 on 2025-08-10 06:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0004_lambdaintegration_aws_access_key_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TMSWebhookEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('reference_number', models.CharField(help_text='TMS consignment reference number', max_length=100)),
                ('customer_reference_number', models.CharField(blank=True, help_text='Customer reference number', max_length=100)),
                ('hub_code', models.CharField(blank=True, help_text='Hub code from webhook', max_length=20)),
                ('type', models.<PERSON><PERSON><PERSON><PERSON>(help_text='Event type from webhook', max_length=50)),
                ('event_time', models.DateTimeField(help_text='Event timestamp from webhook')),
                ('webhook_payload', models.JSONField(help_text='Complete webhook payload from TMS')),
            ],
            options={
                'verbose_name': 'TMS Webhook Event',
                'verbose_name_plural': 'TMS Webhook Events',
                'db_table': 'TMS_WEBHOOK_EVENTS',
                'ordering': ['-event_time'],
                'indexes': [models.Index(fields=['reference_number'], name='TMS_WEBHOOK_referen_4ac134_idx'), models.Index(fields=['hub_code'], name='TMS_WEBHOOK_hub_cod_2fb830_idx'), models.Index(fields=['type'], name='TMS_WEBHOOK_type_d02b77_idx'), models.Index(fields=['event_time'], name='TMS_WEBHOOK_event_t_f5e0ad_idx')],
            },
        ),
    ]
