from django.db import models
from core.models import BaseModel
import json

# Create your models here.


class TMSWebhookEvent(BaseModel):
    """
    Simplified model to store TMS webhook events.
    
    Stores essential consignment information and complete webhook payload.
    """
    
    reference_number = models.CharField(max_length=100, help_text="TMS consignment reference number")
    customer_reference_number = models.CharField(max_length=100, blank=True, help_text="Customer reference number")
    hub_code = models.Char<PERSON>ield(max_length=20, blank=True, help_text="Hub code from webhook")
    type = models.Char<PERSON>ield(max_length=50, help_text="Event type from webhook")
    event_time = models.DateTimeField(help_text="Event timestamp from webhook")
    webhook_payload = models.JSONField(help_text="Complete webhook payload from TMS")
    
    class Meta:
        db_table = 'TMS_WEBHOOK_EVENTS'
        verbose_name = 'TMS Webhook Event'
        verbose_name_plural = 'TMS Webhook Events'
        ordering = ['-event_time']
        indexes = [
            models.Index(fields=['reference_number']),
            models.Index(fields=['hub_code']),
            models.Index(fields=['type']),
            models.Index(fields=['event_time']),
        ]
    
    def __str__(self):
        return f"{self.type} - {self.reference_number} ({self.event_time.strftime('%Y-%m-%d %H:%M')})"
    
    @classmethod
    def create_from_webhook(cls, payload):
        """
        Create a webhook event from the incoming payload.
        
        Args:
            payload: The webhook payload dictionary
            
        Returns:
            TMSWebhookEvent instance
        """
        from django.utils import timezone
        from datetime import datetime
        
        # Extract fields from payload
        reference_number = payload.get('reference_number', '')
        customer_reference_number = payload.get('customer_reference_number', '')
        hub_code = payload.get('hub_code', '')
        event_type = payload.get('type', 'unknown')
        
        # Parse event time
        event_time_str = payload.get('event_time')
        if event_time_str:
            try:
                # Try to parse ISO format timestamp
                event_time = datetime.fromisoformat(event_time_str.replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                event_time = timezone.now()
        else:
            event_time = timezone.now()
        
        return cls.objects.create(
            reference_number=reference_number,
            customer_reference_number=customer_reference_number,
            hub_code=hub_code,
            type=event_type,
            event_time=event_time,
            webhook_payload=payload
        )


class WMSIntegration(BaseModel):
    name = models.CharField(max_length=100)
    base_url = models.URLField(max_length=255, default='https://rzn1-be.stockone.com')
    client_id = models.CharField(max_length=255)
    client_secret = models.CharField(max_length=512)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'WMS_INTEGRATION'

    def __str__(self):
        return self.name



class LambdaIntegration(BaseModel):
    """
    Model for Lambda function integrations (Redis Lambda, Typesense Lambda, etc.)
    """
    LAMBDA_TYPES = (
        ('redis', 'Redis Lambda'),
        ('typesense', 'Typesense Lambda'),
    )

    name = models.CharField(max_length=100)
    lambda_type = models.CharField(max_length=20, choices=LAMBDA_TYPES)
    function_name = models.CharField(max_length=255, help_text="AWS Lambda function name")
    aws_access_key_id = models.CharField(max_length=255, help_text="AWS access key ID", default="")
    aws_secret_access_key = models.CharField(max_length=512, help_text="AWS secret access key", default="")
    region = models.CharField(max_length=50, default='ap-south-1', help_text="AWS region")
    timeout = models.IntegerField(default=30, help_text="Request timeout in seconds")
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'LAMBDA_INTEGRATION'

    def __str__(self):
        return self.name

