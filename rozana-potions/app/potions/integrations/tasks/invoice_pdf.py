from django.utils import timezone
from datetime import datetime

from integrations.services.wms.invoice_service import InvoiceService
from potions.celery import app as celery_app

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('invoice_pdf')


@celery_app.task(bind=True, max_retries=3)
def process_invoice_task(self, order_reference, warehouse, invoice_date_str=None, wms_integration_name='default', update_oms=True):
    """
    Celery task to process invoice: fetch HTML, convert to PDF, upload to S3, and update OMS.
    
    Args:
        order_reference (str): Order reference number (same as order_id in OMS)
        warehouse (str): Warehouse name
        invoice_date_str (str, optional): Invoice date as ISO string
        wms_integration_name (str): Name of WMS integration to use
        update_oms (bool, optional): Whether to update OMS database. Defaults to True.
    """
    try:
        logger.info(f"Starting invoice processing for order: {order_reference}")
        
        # Parse invoice date if provided
        invoice_date = None
        if invoice_date_str:
            try:
                invoice_date = datetime.fromisoformat(invoice_date_str.replace('Z', '+00:00'))
            except ValueError:
                logger.warning(f"Invalid invoice date format: {invoice_date_str}, using current time")
                invoice_date = timezone.now()
        else:
            invoice_date = timezone.now()
        
        # Initialize invoice service with WMS integration
        invoice_service = InvoiceService(wms_integration_name=wms_integration_name)
        
        # Process the invoice (now returns a dict with comprehensive results)
        result = invoice_service.process_invoice(
            order_reference=order_reference,
            warehouse=warehouse,
            invoice_date=invoice_date,
            update_oms=update_oms
        )
        
        logger.info(f"Invoice processing completed successfully for {order_reference}. PDF URL: {result['s3_url']}")
        
        # Log OMS update result if OMS update was enabled
        if update_oms and result.get('oms_update'):
            oms_result = result['oms_update']
            if oms_result['success']:
                logger.info(f"OMS database updated successfully for order {order_reference}")
            else:
                logger.warning(f"OMS database update failed for order {order_reference}: {oms_result.get('error')}")
        
        return {
            'status': 'success',
            'order_reference': order_reference,
            'warehouse': warehouse,
            'pdf_url': result['s3_url'],
            'order_id': order_id,
            'oms_update': result.get('oms_update')
        }
        
    except Exception as exc:
        logger.error(f"Invoice processing failed for order {order_reference}: {str(exc)}")
        
        # Retry the task if retries are available
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying invoice processing task. Attempt {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60 * (self.request.retries + 1), exc=exc)
        
        return {
            'status': 'error',
            'order_reference': order_reference,
            'warehouse': warehouse,
            'message': str(exc),
            'retries_exhausted': True
        }
