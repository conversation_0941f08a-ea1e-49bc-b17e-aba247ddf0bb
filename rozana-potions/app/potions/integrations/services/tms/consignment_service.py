import requests
from typing import Dict, Any, List
from decimal import Decimal
from django.conf import settings
from integrations.services.tms.auth_service import make_tms_request, TMSAPIError
from core.models import HubMaster, FacilityHubMapping
from django.db import connections

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('tms_consignment')


class BaseTMSCNService:
    """
    Base service class for TMS Consignment Note operations.
    
    Provides common functionality for creating consignment notes via TMS API.
    """
    
    def validate_hub(self, hub_code: str) -> None:
        """
        Validate if the hub is allowed for consignment creation based on environment.
        
        Args:
            hub_code: The hub code to validate
            
        Raises:
            TMSAPIError: If the hub is not allowed
        """
        environment = getattr(settings, 'APPLICATION_ENVIRONMENT', 'PRODUCTION')
        is_allowed, error_message = HubMaster.is_hub_allowed(hub_code, environment)
        
        if not is_allowed:
            logger.error(f"Hub validation failed: {error_message}")
            raise TMSAPIError(error_message)
        
        logger.info(f"Hub '{hub_code}' validated successfully for environment '{environment}'")
    
    def _sanitize_for_json(self, data: Any) -> Any:
        """Recursively convert Decimals to float for JSON serialization."""
        if isinstance(data, Decimal):
            return float(data)
        if isinstance(data, dict):
            return {k: self._sanitize_for_json(v) for k, v in data.items()}
        if isinstance(data, list):
            return [self._sanitize_for_json(v) for v in data]
        return data

    def create_consignment(self, consignment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a consignment note via TMS API.
        
        Args:
            consignment_data: Dictionary containing consignment details
            
        Returns:
            Dict containing the API response
            
        Raises:
            TMSAPIError: If the API request fails
        """
        endpoint = "/api/client/integration/consignment/upload/softdata/v2"
        
        # Validate hub before creating consignment
        hub_code = consignment_data.get('hub_code')
        if hub_code:
            self.validate_hub(hub_code)

        # Sanitize payload: convert Decimals to float recursively
        # consignment_data = self._sanitize_for_json(consignment_data)

        try:
            logger.info(f"Creating consignment with reference: {consignment_data.get('reference_number', 'N/A')}")
            logger.debug(f"Consignment payload: {consignment_data}")
            
            response = make_tms_request(
                method='POST',
                endpoint=endpoint,
                json=consignment_data
            )
            
            result = response.json()
            logger.info(f"Consignment created successfully: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to create consignment: {str(e)}")
            raise TMSAPIError(f"Failed to create consignment: {str(e)}") from e


class ForwardCNService(BaseTMSCNService):
    """
    Service class for Forward Consignment Note (CN) operations.
    
    Handles delivery consignments from warehouse to customer.
    """
    
    def create(
        self,
        customer_code: str,
        reference_number: str,
        customer_reference_number: str,
        hub_code: str,
        cod_amount: float,
        destination_details: Dict[str, Any],
        pieces_detail_list: List[Dict[str, Any]],
        cod_favor_of: str = "Rozana",
        cod_collection_mode: str = "",
    ) -> Dict[str, Any]:
        """
        Create a forward consignment (delivery from warehouse to customer).
        
        Args:
            customer_code: Customer code (e.g., "Rozana")
            reference_number: Unique reference number for the consignment
            customer_reference_number: Customer's internal reference number
            hub_code: Hub code for pickup/delivery
            cod_amount: Cash on delivery amount
            destination_details: Dictionary containing delivery address and contact info
            pieces_detail: List of items/pieces in the consignment
            cod_favor_of: COD collection entity (default: "Rozana")
            cod_collection_mode: COD collection mode (default: "")
            reference_image_url: Optional reference image URL
            customs_value: Optional customs value information
            
        Returns:
            Dictionary containing the API response
        """
        consignment_data = {
            "customer_code": customer_code,
            "service_type_id": "PREMIUM",
            "action_type": "delivery",
            "consignment_type": "forward",
            "movement_type": "forward",
            "verify_otp_on_delivery": True,
            "reference_number": reference_number,
            "cod_favor_of": cod_favor_of,
            "cod_collection_mode": cod_collection_mode,
            "cod_amount": cod_amount,
            "currency": "INR",
            "customer_reference_number": customer_reference_number,
            "hub_code": hub_code,
            "reference_image_url" : "https://shipsy-public-assets.s3.amazonaws.com/shipsyflamingo/logo.png",
            "customs_value" : {
                "amount" : "100",
                "currency" : "USD" # have to check the significance of this field
            },
            "destination_details": {
                "company_name" : destination_details['company_name'],
                "name" : destination_details['name'],
                "phone" : destination_details['phone'],
                "address_line_1" : destination_details['address_line_1'],
                "pincode" : destination_details['pincode'],
                "district" : destination_details['district'],
                "city" : destination_details['city'],
                "state" : destination_details['state'],
                "country" : destination_details['country'],
                "latitude" : destination_details.get('latitude'),
                "longitude" : destination_details.get('longitude')
            }
        }

        pieces_detail = []
        for piece in pieces_detail_list:
            details = {
                "description" : piece.get('description', ''),
                "quantity" : piece['quantity'],
                "declared_value" : piece['declared_value'],
                "piece_product_code" : piece['piece_product_code'],
                "cod_amount" : piece['cod_amount'],
                "reference_image_url" : piece['reference_image_url'],
            }
            pieces_detail.append(details)
        
        consignment_data['pieces_detail'] = pieces_detail
        return self.create_consignment(consignment_data)

class TrackCNService(BaseTMSCNService):
    """
    Service class for Track Consignment Note (CN) operations.
    """
    
    def track(self, reference_number: str) -> Dict[str, Any]:
        """
        Track a consignment (delivery from warehouse to customer).
        
        Args:
            reference_number: Unique reference number for the consignment
            
        Returns:
            Dictionary containing the API response
        """
        endpoint = "/api/client/integration/consignment/track"
        consignment_data = {
            "reference_number": reference_number,
        }
        try:
            logger.info(f"Tracking consignment with reference: {consignment_data.get('reference_number', 'N/A')}")

            response = make_tms_request(
                method='GET',
                endpoint=endpoint,
                params=consignment_data
            )

            result = response.json()
            logger.info(f"Consignment tracked successfully: {result}")
            return result

        except Exception as e:
            logger.error(f"Failed to track consignment: {str(e)}")
            raise TMSAPIError(f"Failed to track consignment: {str(e)}") from e

class ReverseCNService(BaseTMSCNService):
    """
    Service class for Reverse Consignment Note (CN) operations.
    
    Handles return consignments from customer to warehouse.
    """
    
    def prepare_from_oms(self, return_reference: str) -> Dict[str, Any]:
        """
        Prepare Reverse CN input by querying OMS DB using the provided return_reference.

        Returns a dict with keys matching ReverseCNService.create parameters:
        - customer_reference_number (orders.order_id)
        - hub_code (orders.facility_name)
        - hub_address_line_1 (empty; inferred at destination by hub code)
        - origin_details (pickup address/contact at customer)
        - pieces_detail_list (from return_items joined with order_items; COD = sale_price * qty)
        - return_code (echo back as return_reference)

        Note: Adjust SQL column names/tables below to match OMS schema if different.
        """
        cursor = None
        try:
            oms_db = connections['oms']
            cursor = oms_db.cursor()

            # Header and address: returns -> orders -> order_addresses
            header_sql = """
                SELECT 
                    r.return_reference,
                    o.order_id AS oms_order_id,
                    o.facility_name,
                    oa.full_name AS customer_name,
                    oa.phone_number AS customer_phone,
                    oa.address_line1,
                    COALESCE(oa.address_line2, '') AS address_line2,
                    oa.city,
                    oa.state,
                    oa.postal_code,
                    oa.country,
                    COALESCE(oa.latitude, 0.0) AS latitude,
                    COALESCE(oa.longitude, 0.0) AS longitude
                FROM returns r
                JOIN orders o ON o.id = r.order_id
                LEFT JOIN order_addresses oa ON oa.order_id = o.id
                WHERE r.return_reference = %s
                ORDER BY oa.id ASC
                LIMIT 1
            """

            cursor.execute(header_sql, [return_reference])
            header_row = cursor.fetchone()
            if not header_row:
                raise TMSAPIError(f"Return not found in OMS for return_reference={return_reference}")

            header_cols = [col[0] for col in cursor.description]
            header = dict(zip(header_cols, header_row))

            # Items: return_items join order_items to get sku/wh_sku and pricing
            items_sql = """
                SELECT 
                    ri.quantity_returned,
                    ri.unit_price,
                    ri.sale_price,
                    oi.wh_sku AS piece_product_code,
                    oi.name as description,
                    ri.refund_amount as cod_amount
                FROM return_items ri
                JOIN returns r ON r.id = ri.return_id
                JOIN order_items oi ON oi.id = ri.order_item_id
                WHERE r.return_reference = %s
            """

            cursor.execute(items_sql, [return_reference])
            items_rows = cursor.fetchall()
            items_cols = [col[0] for col in cursor.description]

            pieces_detail_list: List[Dict[str, Any]] = []
            for row in items_rows:
                item = dict(zip(items_cols, row))
                qty = float(item.get('quantity_returned') or 0)
                piece_product_code = item.get('piece_product_code') or item.get('sku') or ''
                description = item.get('description') or piece_product_code

                pieces_detail_list.append({
                    "description": description,
                    "quantity": qty,
                    "declared_value": float(item.get('cod_amount', 0)),
                    "piece_product_code": piece_product_code,
                    "cod_amount": float(item.get('cod_amount', 0)),
                    "reference_image_url": "https://dev.rozana.in/public/uploads/products/thumbnail/500x500.png",
                    "return_quantity": qty,
                })

            origin_details = {
                "company_name": "Rozana",
                "name": header.get('customer_name', ''),
                "phone": header.get('customer_phone', ''),
                "address_line_1": header.get('address_line1', ''),
                "pincode": header.get('postal_code', ''),
                "district": header.get('city', ''),
                "city": header.get('city', ''),
                "state": header.get('state', ''),
                "country": header.get('country', 'india'),
                "latitude": float(header.get('latitude') or 0.0),
                "longitude": float(header.get('longitude') or 0.0),
            }

            # Get hub_code from facility mapping
            facility_name = header.get('facility_name', '')
            try:
                mapping = FacilityHubMapping.objects.select_related('facility', 'hub').get(
                    facility__name=facility_name, is_active=True
                )
                hub_code = mapping.hub.hub_code
            except FacilityHubMapping.DoesNotExist:
                raise TMSAPIError(f"No active hub mapping found for facility '{facility_name}'")
            
            prepared: Dict[str, Any] = {
                "customer_reference_number": header.get('oms_order_id', ''),
                "hub_code": hub_code,
                "hub_address_line_1": "",
                "origin_details": origin_details,
                "pieces_details": pieces_detail_list,
                "return_code": return_reference,
            }

            logger.info(f"Prepared Reverse CN input from OMS for return_reference={return_reference}")
            print(prepared)
            return prepared

        except Exception as e:
            logger.error(f"Failed to prepare Reverse CN input from OMS for return_reference={return_reference}: {e}")
            raise TMSAPIError(f"Failed to prepare Reverse CN input from OMS: {e}")
        finally:
            if cursor:
                cursor.close()

    def create(
        self,
        customer_reference_number: str,
        hub_code: str,
        hub_address_line_1: str,
        origin_details: Dict[str, Any],
        pieces_detail_list: List[Dict[str, Any]],
        return_code: str = "",
    ) -> Dict[str, Any]:
        """
        Create a reverse consignment (return from customer to warehouse).
        
        Args:
            reference_number: Unique reference number for the consignment
            customer_reference_number: Customer's internal reference number
            hub_code: Hub code for pickup/delivery
            pickup_details: Dictionary containing pickup address and contact info (customer location)
            destination_details: Dictionary containing destination address (warehouse/hub)
            pieces_detail_list: List of items/pieces being returned
            
        Returns:
            Dictionary containing the API response
        """
        consignment_data = {
            # "customer_code": "Rozana",
            "service_type_id": "PREMIUM",
            "action_type": "single_pickup",
            "consignment_type": "reverse",
            "movement_type": "forward",
            "verify_otp_on_delivery": True,
            "reference_number": return_code,
            "cod_favor_of": "Rozana",
            "cod_collection_mode": "", # cash
            "currency": "INR",
            "customer_reference_number": customer_reference_number, # Order ID/ Invoice Number
            "hub_code": hub_code,
            "origin_details": {
                "company_name": "Rozana",
                "name": origin_details['name'],
                "phone": origin_details['phone'],
                "address_line_1": origin_details['address_line_1'],
                "pincode": origin_details['pincode'],
                "district": origin_details['district'],
                "city": origin_details['city'],
                "state": origin_details['state'],
                "country": origin_details['country'],
                "latitude": origin_details.get('latitude'),
                "longitude": origin_details.get('longitude')
            },
            "destination_details": {
                "address_hub_code": hub_code,
                "address_line_1": hub_address_line_1, 
            },
        }

        # Create pieces detail
        pieces_detail = []
        reference_image_url = "https://dev.rozana.in/public/uploads/products/thumbnail/500x500.png"
        payable_amount = 0
        for piece in pieces_detail_list:
            return_quantity = piece['return_quantity']
            details = {
                "description" : piece.get('description', ''),
                "quantity" : return_quantity,
                "declared_value" : piece['declared_value'], # mrp * return_quantity
                "piece_product_code" : piece['piece_product_code'],
                "cod_amount" : piece['cod_amount'], # sale_price * return_quantity
                "reference_image_url" : reference_image_url,
            }
            payable_amount += piece['cod_amount']
            pieces_detail.append(details)

        # Add pieces detail to consignment data and final payable amount
        consignment_data['pieces_detail'] = pieces_detail
        consignment_data['cod_amount'] = payable_amount
        logger.info(f"Consignment data: {consignment_data}")
        return self.create_consignment(consignment_data)

