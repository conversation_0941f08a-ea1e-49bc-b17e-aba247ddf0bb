import os


class WhatsAppMessageTemplates:
    """Predefined WhatsApp message templates for different events"""
    
    @staticmethod
    def order_created(customer_data, order_id: str) -> str:
        """Template for order creation notification"""
        return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id} जिसकी कीमत ₹{customer_data.amount} है, {customer_data.delivery_time} घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
    
    @staticmethod
    def order_invoiced(customer_data, order_id: str, invoice_number: str = None) -> str:
        """Template for order invoiced notification"""
        invoice_text = f" (Invoice: {invoice_number})" if invoice_number else ""
        # return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id}{invoice_text} के लिए बिल तैयार हो गया है। जल्द ही आपका ऑर्डर डिलीवर कर दिया जाएगा। -Team Rozana"
        return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id} जिसकी कीमत ₹{customer_data.amount} है, {customer_data.delivery_time} घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
    
    @staticmethod
    def order_delivered(customer_data, order_id: str) -> str:
        """Template for order delivered notification"""
        # return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id} सफलतापूर्वक डिलीवर कर दिया गया है। Rozana चुनने के लिए धन्यवाद! -Team Rozana"
        return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id} जिसकी कीमत ₹{customer_data.amount} है, {customer_data.delivery_time} घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
    
    @staticmethod
    def cn_accepted(customer_data, cn_number: str, amount: float) -> str:
        """Template for consignment note accepted notification"""
        # return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {cn_number} ₹{amount} के लिए है और यह 60 minutes के भीतर डिलीवर किया जाएगा। - Team Rozana"
        return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {cn_number} जिसकी कीमत ₹{customer_data.amount} है, {customer_data.delivery_time} घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
    
    @staticmethod
    def cn_delivered(customer_data, cn_number: str) -> str:
        """Template for consignment note delivered notification"""
        # return f"नमस्ते {customer_data.full_name}, आपका consignment {cn_number} सफलतापूर्वक डिलीवर कर दिया गया है। -Team Rozana"
        return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {cn_number} जिसकी कीमत ₹{customer_data.amount} है, {customer_data.delivery_time} घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
    
    @staticmethod
    def pickup_completed(customer_data, order_id: str) -> str:
        """Template for pickup completed notification"""
        # return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id} pickup के लिए तैयार है। जल्द ही डिलीवरी शुरू होगी। -Team Rozana"
        return f"नमस्ते {customer_data.full_name}, आपका ऑर्डर {order_id} जिसकी कीमत ₹{customer_data.amount} है, {customer_data.delivery_time} घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
