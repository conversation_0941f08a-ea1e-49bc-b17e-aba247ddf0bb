import boto3

from datetime import datetime
from typing import Optional, Dict, Any
from django.conf import settings
from botocore.exceptions import ClientError, NoCredentialsError

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('s3_service')


class S3Service:
    """
    Service class for handling AWS S3 operations.
    Provides methods for uploading files, generating URLs, and managing S3 objects.
    """
    
    def __init__(self):
        """
        Initialize S3 service with AWS credentials and configuration.
        """
        self.aws_access_key_id = getattr(settings, 'AWS_ACCESS_KEY_ID', None)
        self.aws_secret_access_key = getattr(settings, 'AWS_SECRET_ACCESS_KEY', None)
        self.aws_region = getattr(settings, 'AWS_S3_REGION_NAME', 'ap-south-1')
        self.bucket_name = getattr(settings, 'AWS_STORAGE_BUCKET_NAME', None)
        
        if not all([self.aws_access_key_id, self.aws_secret_access_key, self.bucket_name]):
            raise ValueError("AWS S3 configuration is incomplete. Please check settings.")
        
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region
        )
        
        logger.info(f"S3Service initialized for bucket: {self.bucket_name}")
    
    def upload_file(
        self, 
        file_content: bytes, 
        key: str, 
        content_type: str = 'application/pdf',
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """
        Upload file content to S3 bucket.
        
        Args:
            file_content: Binary content of the file
            key: S3 object key (path/filename)
            content_type: MIME type of the file
            metadata: Optional metadata to attach to the object
            
        Returns:
            S3 URL of the uploaded file
            
        Raises:
            Exception: If upload fails
        """
        try:
            # Prepare upload parameters
            upload_params = {
                'Bucket': self.bucket_name,
                'Key': key,
                'Body': file_content,
                'ContentType': content_type
            }
            
            # Add metadata if provided
            if metadata:
                upload_params['Metadata'] = metadata
            
            # Upload file to S3
            self.s3_client.put_object(**upload_params)
            
            # Generate S3 URL
            s3_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{key}"
            
            logger.info(f"Successfully uploaded file to S3: {s3_url}")
            return s3_url
            
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise Exception("AWS credentials not found")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            logger.error(f"S3 upload failed with error {error_code}: {str(e)}")
            raise Exception(f"S3 upload failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during S3 upload: {str(e)}")
            raise Exception(f"S3 upload failed: {str(e)}")
    
    def generate_invoice_key(
        self, 
        warehouse: str, 
        order_reference: str, 
        invoice_date: Optional[datetime] = None
    ) -> str:
        """
        Generate S3 key for invoice PDF files.
        
        Args:
            warehouse: Warehouse name
            order_reference: Order reference number
            invoice_date: Invoice date (defaults to current date)
            
        Returns:
            S3 key in format: invoices/{warehouse}/{year}/{month}/{order_reference}.pdf
        """
        if invoice_date is None:
            invoice_date = datetime.now()
        
        year = invoice_date.strftime('%Y')
        month = invoice_date.strftime('%m')
        
        # Sanitize warehouse name and order reference for S3 key
        safe_warehouse = warehouse.replace(' ', '_').replace('/', '_')
        safe_order_ref = order_reference.replace(' ', '_').replace('/', '_')
        
        key = f"invoices/{safe_warehouse}/{year}/{month}/{safe_order_ref}.pdf"
        
        logger.debug(f"Generated S3 key: {key}")
        return key
    
    def upload_invoice_pdf(
        self, 
        pdf_content: bytes, 
        warehouse: str, 
        order_reference: str,
        invoice_date: Optional[datetime] = None,
        additional_metadata: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """
        Upload invoice PDF to S3 with organized structure.
        
        Args:
            pdf_content: Binary content of the PDF
            warehouse: Warehouse name
            order_reference: Order reference number
            invoice_date: Invoice date (defaults to current date)
            additional_metadata: Additional metadata to attach
            
        Returns:
            Dict containing both 'key' and 'url' of the uploaded PDF
        """
        # Generate S3 key
        key = self.generate_invoice_key(warehouse, order_reference, invoice_date)
        
        # Prepare metadata
        metadata = {
            'warehouse': warehouse,
            'order_reference': order_reference,
            'upload_timestamp': datetime.now().isoformat(),
            'content_type': 'invoice_pdf'
        }
        
        if invoice_date:
            metadata['invoice_date'] = invoice_date.isoformat()
        
        if additional_metadata:
            metadata.update(additional_metadata)
        
        # Upload to S3
        s3_url = self.upload_file(
            file_content=pdf_content,
            key=key,
            content_type='application/pdf',
            metadata=metadata
        )
        
        return {
            'key': key,
            'url': s3_url
        }
    
    def check_file_exists(self, key: str) -> bool:
        """
        Check if a file exists in S3 bucket.
        
        Args:
            key: S3 object key
            
        Returns:
            True if file exists, False otherwise
        """
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            else:
                logger.error(f"Error checking S3 file existence: {str(e)}")
                raise Exception(f"Error checking S3 file: {str(e)}")
    
    def get_file_metadata(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for an S3 object.
        
        Args:
            key: S3 object key
            
        Returns:
            Dictionary containing object metadata or None if not found
        """
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return {
                'size': response.get('ContentLength'),
                'last_modified': response.get('LastModified'),
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {})
            }
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            else:
                logger.error(f"Error getting S3 file metadata: {str(e)}")
                raise Exception(f"Error getting S3 metadata: {str(e)}")
    
    def delete_file(self, key: str) -> bool:
        """
        Delete a file from S3 bucket.
        
        Args:
            key: S3 object key
            
        Returns:
            True if deletion was successful
        """
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
            logger.info(f"Successfully deleted S3 object: {key}")
            return True
        except ClientError as e:
            logger.error(f"Error deleting S3 file: {str(e)}")
            raise Exception(f"Error deleting S3 file: {str(e)}")
