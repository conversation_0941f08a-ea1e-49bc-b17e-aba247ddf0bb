"""
WMS Return Service

This module handles order return processing with the WMS (Warehouse Management System).
It provides functionality to notify WMS about returns, process return inventory,
and update return status in OMS.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from django.db import connections

from integrations.models import WMSIntegration
from integrations.services.wms.auth_service import (
    WMSAuth,
    WMSAuthenticationError,
    WMSAPIError,
)

from potions.logging.utils import get_app_logger
logger = get_app_logger('wms_return_service')


class WMSReturnService:
    """Service class for WMS return operations."""

    def __init__(self, wms_integration_name: str = 'default'):
        """
        Initialize WMS Return Service.
        
        Args:
            wms_integration_name: Name of the WMS integration to use
        """
        self._init_wms_auth(wms_integration_name)

    def _init_wms_auth(self, integration_name: str) -> None:
        """Initialize WMS authentication using integration settings."""
        try:
            wms_config = WMSIntegration.objects.filter(
                name=integration_name,
                is_active=True,
            ).first()
            if not wms_config:
                wms_config = WMSIntegration.objects.filter(is_active=True).first()
            if not wms_config:
                raise ValueError('No active WMS integration found')
            
            self.wms_auth = WMSAuth(
                base_url=wms_config.base_url,
                client_id=wms_config.client_id,
                client_secret=wms_config.client_secret,
            )
            logger.info(
                'Initialized WMS return service with integration: %s',
                wms_config.name,
            )
        except Exception as exc:
            logger.error('Failed to initialize WMS authentication: %s', exc)
            raise ValueError(f'WMS authentication initialization failed: {exc}')

    def fetch_return_data_from_oms(self, return_reference: str) -> Optional[Dict[str, Any]]:
        """
        Fetch return data from OMS database.
        
        Args:
            return_reference: Unique return reference identifier
            
        Returns:
            Dict containing return data or None if not found
        """
        try:
            oms_db = connections['oms']
            cursor = oms_db.cursor()

            # Fetch return details with order information
            return_query = """
                SELECT 
                    r.return_reference,
                    r.order_id,
                    r.return_reason,
                    r.return_status,
                    r.created_at,
                    r.updated_at,
                    o.order_id as oms_order_id,
                    o.facility_name,
                    o.facility_id,
                    o.customer_name,
                    o.total_amount
                FROM returns r
                JOIN orders o ON o.id = r.order_id
                WHERE r.return_reference = %s
            """

            cursor.execute(return_query, [return_reference])
            return_result = cursor.fetchone()

            if not return_result:
                logger.error(f"Return {return_reference} not found in OMS database")
                return None

            # Fetch return items
            items_query = """
                SELECT 
                    ri.id,
                    ri.sku,
                    ri.product_name,
                    ri.quantity,
                    ri.unit_price,
                    ri.return_reason,
                    oi.batch_number,
                    oi.expiry_date
                FROM return_items ri
                JOIN order_items oi ON oi.id = ri.order_item_id
                WHERE ri.return_id = (
                    SELECT id FROM returns WHERE return_reference = %s
                )
            """

            cursor.execute(items_query, [return_reference])
            items_results = cursor.fetchall()

            # Structure the return data
            return_data = {
                'return_reference': return_result[0],
                'order_id': return_result[1],
                'return_reason': return_result[2],
                'return_status': return_result[3],
                'created_at': return_result[4],
                'updated_at': return_result[5],
                'oms_order_id': return_result[6],
                'facility_name': return_result[7],
                'facility_id': return_result[8],
                'customer_name': return_result[9],
                'total_amount': return_result[10],
                'items': []
            }

            # Add return items
            for item in items_results:
                return_data['items'].append({
                    'id': item[0],
                    'sku': item[1],
                    'product_name': item[2],
                    'quantity': item[3],
                    'unit_price': item[4],
                    'return_reason': item[5],
                    'batch_number': item[6],
                    'expiry_date': item[7]
                })

            logger.info(f"Successfully fetched return data for {return_reference}")
            return return_data

        except Exception as e:
            logger.error(f"Error fetching return data for {return_reference}: {str(e)}")
            return None

    def create_return_order_in_wms(self, return_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a return order in WMS system.
        
        Args:
            return_data: Return data dictionary from OMS
            
        Returns:
            Dict containing WMS response and status
        """
        try:
            # Prepare WMS return order payload
            wms_payload = self._prepare_wms_return_payload(return_data)
            
            # WMS API endpoint for creating return orders
            endpoint = '/api/v2/inbound/returns/'
            headers = {
                'warehouse': return_data.get('facility_id', return_data.get('facility_name')),
                'Content-Type': 'application/json'
            }
            
            logger.info(
                f"Creating return order in WMS for return_reference: {return_data['return_reference']}"
            )
            
            response = self.wms_auth.make_authenticated_request(
                method='POST',
                endpoint=endpoint,
                headers=headers,
                json=wms_payload
            )
            
            try:
                response_data = response.json()
            except ValueError:
                response_data = {'detail': response.text}
            
            if response.status_code in [200, 201]:
                logger.info(
                    f"Successfully created return order in WMS for {return_data['return_reference']}"
                )
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'data': response_data,
                    'wms_return_id': response_data.get('return_id', response_data.get('id'))
                }
            else:
                logger.error(
                    f"Failed to create return order in WMS: {response.status_code} - {response_data}"
                )
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'data': response_data,
                    'error': f"WMS API returned status {response.status_code}"
                }
                
        except (WMSAuthenticationError, WMSAPIError) as exc:
            logger.error(f'WMS API error creating return order: {exc}')
            return {
                'success': False,
                'error': f'WMS API error: {str(exc)}',
                'status_code': None
            }
        except Exception as exc:
            logger.error(f'Unexpected error creating return order in WMS: {exc}')
            return {
                'success': False,
                'error': f'Unexpected error: {str(exc)}',
                'status_code': None
            }

    def _prepare_wms_return_payload(self, return_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare WMS API payload for return order creation.
        
        Args:
            return_data: Return data from OMS
            
        Returns:
            Dict containing WMS-formatted payload
        """
        # Prepare items for WMS
        wms_items = []
        for item in return_data.get('items', []):
            wms_items.append({
                'sku': item['sku'],
                'product_name': item['product_name'],
                'quantity': item['quantity'],
                'unit_price': float(item['unit_price']) if item['unit_price'] else 0.0,
                'return_reason': item.get('return_reason', 'Customer Return'),
                'batch_number': item.get('batch_number'),
                'expiry_date': item.get('expiry_date').isoformat() if item.get('expiry_date') else None
            })

        # Prepare WMS payload
        payload = {
            'return_reference': return_data['return_reference'],
            'original_order_id': return_data['oms_order_id'],
            'customer_name': return_data.get('customer_name', ''),
            'return_reason': return_data.get('return_reason', 'Customer Return'),
            'facility_id': return_data.get('facility_id', return_data.get('facility_name')),
            'return_type': 'customer_return',
            'expected_return_date': datetime.now().isoformat(),
            'items': wms_items,
            'metadata': {
                'source': 'OMS',
                'created_by': 'potions_integration',
                'return_status': return_data.get('return_status', 'pending')
            }
        }

        return payload

    def update_return_status_in_oms(self, return_reference: str, status: str,
                                   wms_return_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Update return status in OMS database.

        Args:
            return_reference: Return reference identifier
            status: New return status
            wms_return_id: WMS return ID (optional)

        Returns:
            Dict containing update result
        """
        try:
            oms_db = connections['oms']
            cursor = oms_db.cursor()

            # Update return status
            update_query = """
                UPDATE returns
                SET return_status = %s,
                    wms_return_id = COALESCE(%s, wms_return_id),
                    updated_at = NOW()
                WHERE return_reference = %s
            """

            cursor.execute(update_query, [status, wms_return_id, return_reference])
            updated_count = cursor.rowcount
            oms_db.commit()

            if updated_count > 0:
                logger.info(f"Updated return {return_reference} status to {status}")
                return {
                    'success': True,
                    'return_reference': return_reference,
                    'new_status': status,
                    'wms_return_id': wms_return_id,
                    'updated_count': updated_count
                }
            else:
                logger.warning(f"No return found with reference: {return_reference}")
                return {
                    'success': False,
                    'error': f'Return not found with reference: {return_reference}',
                    'return_reference': return_reference
                }

        except Exception as e:
            logger.error(f"Error updating return status for {return_reference}: {str(e)}")
            return {
                'success': False,
                'error': f'Database error: {str(e)}',
                'return_reference': return_reference
            }

    def process_order_return_to_wms(self, order_id: str, return_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main function to process order return to WMS.
        This is the function called from TMS webhook handlers.

        Args:
            order_id: Original order ID
            return_details: Return details from TMS or OMS
                - return_reference: Unique return identifier
                - return_reason: Reason for return
                - items: List of returned items (optional)
                - warehouse: Target warehouse

        Returns:
            Dict containing processing result
        """
        try:
            return_reference = return_details.get('return_reference')
            if not return_reference:
                logger.error(f"No return_reference provided for order {order_id}")
                return {
                    'success': False,
                    'error': 'return_reference is required',
                    'order_id': order_id
                }

            logger.info(f"Processing order return to WMS: order_id={order_id}, return_reference={return_reference}")

            # Step 1: Fetch return data from OMS
            return_data = self.fetch_return_data_from_oms(return_reference)
            if not return_data:
                logger.error(f"Failed to fetch return data for {return_reference}")
                return {
                    'success': False,
                    'error': 'Return data not found in OMS',
                    'order_id': order_id,
                    'return_reference': return_reference
                }

            # Step 2: Create return order in WMS
            wms_result = self.create_return_order_in_wms(return_data)

            # Step 3: Update return status in OMS based on WMS result
            if wms_result['success']:
                # Update OMS with successful WMS creation
                oms_update = self.update_return_status_in_oms(
                    return_reference=return_reference,
                    status='wms_notified',
                    wms_return_id=wms_result.get('wms_return_id')
                )

                logger.info(f"Successfully processed return {return_reference} to WMS")
                return {
                    'success': True,
                    'order_id': order_id,
                    'return_reference': return_reference,
                    'wms_result': wms_result,
                    'oms_update': oms_update,
                    'message': 'Return successfully processed to WMS'
                }
            else:
                # Update OMS with failed WMS creation
                oms_update = self.update_return_status_in_oms(
                    return_reference=return_reference,
                    status='wms_failed'
                )

                logger.error(f"Failed to process return {return_reference} to WMS: {wms_result.get('error')}")
                return {
                    'success': False,
                    'order_id': order_id,
                    'return_reference': return_reference,
                    'wms_result': wms_result,
                    'oms_update': oms_update,
                    'error': f"WMS processing failed: {wms_result.get('error')}"
                }

        except Exception as e:
            logger.error(f"Exception in process_order_return_to_wms for order {order_id}: {str(e)}")
            return {
                'success': False,
                'order_id': order_id,
                'error': f'Processing exception: {str(e)}',
                'return_details': return_details
            }

    def get_return_status_from_wms(self, wms_return_id: str, warehouse: str) -> Dict[str, Any]:
        """
        Get return status from WMS.

        Args:
            wms_return_id: WMS return identifier
            warehouse: Warehouse identifier

        Returns:
            Dict containing return status from WMS
        """
        try:
            endpoint = f'/api/v2/inbound/returns/{wms_return_id}/'
            headers = {'warehouse': warehouse}

            logger.info(f"Fetching return status from WMS: {wms_return_id}")

            response = self.wms_auth.make_authenticated_request(
                method='GET',
                endpoint=endpoint,
                headers=headers
            )

            try:
                response_data = response.json()
            except ValueError:
                response_data = {'detail': response.text}

            if response.status_code == 200:
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'data': response_data,
                    'return_status': response_data.get('status', 'unknown')
                }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'data': response_data,
                    'error': f"WMS API returned status {response.status_code}"
                }

        except (WMSAuthenticationError, WMSAPIError) as exc:
            logger.error(f'WMS API error fetching return status: {exc}')
            return {
                'success': False,
                'error': f'WMS API error: {str(exc)}'
            }
        except Exception as exc:
            logger.error(f'Unexpected error fetching return status: {exc}')
            return {
                'success': False,
                'error': f'Unexpected error: {str(exc)}'
            }


# Convenience function for backward compatibility and easy import
def process_order_return_to_wms(order_id: str, return_details: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function to process order return to WMS.
    This function can be imported and used directly from TMS webhook handlers.

    Args:
        order_id: Original order ID
        return_details: Return details dictionary

    Returns:
        Dict containing processing result
    """
    service = WMSReturnService()
    return service.process_order_return_to_wms(order_id, return_details)
