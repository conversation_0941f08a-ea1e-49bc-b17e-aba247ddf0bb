import logging
from integrations.models import WMSIntegration
from integrations.services.wms.auth_service import (
    W<PERSON><PERSON>uth,
    WMSAuthenticationError,
    WMSAPIError,
)

from potions.logging.utils import get_app_logger
logger = get_app_logger('wms_order_service')


class WMSOrderService:
    """Service class for WMS order operations like cancellation."""

    def __init__(self, wms_integration_name: str = 'default'):
        self._init_wms_auth(wms_integration_name)

    def _init_wms_auth(self, integration_name: str) -> None:
        """Initialize WMS authentication using integration settings."""
        try:
            wms_config = WMSIntegration.objects.filter(
                name=integration_name,
                is_active=True,
            ).first()
            if not wms_config:
                wms_config = WMSIntegration.objects.filter(is_active=True).first()
            if not wms_config:
                raise ValueError('No active WMS integration found')
            self.wms_auth = WMSAuth(
                base_url=wms_config.base_url,
                client_id=wms_config.client_id,
                client_secret=wms_config.client_secret,
            )
            logger.info(
                'Initialized WMS order service with integration: %s',
                wms_config.name,
            )
        except Exception as exc:
            logger.error('Failed to initialize WMS authentication: %s', exc)
            raise ValueError(f'WMS authentication initialization failed: {exc}')


    def cancel_order(self, order_reference: str, warehouse: str) -> dict:
        """Cancel an order in WMS via StockOne API."""
        endpoint = '/api/v2/outbound/orders/'
        params = {'order_reference': order_reference}
        headers = {'warehouse': warehouse}
        logger.info(
            'Cancelling WMS order %s for warehouse %s',
            order_reference,
            warehouse,
        )
        try:
            response = self.wms_auth.make_authenticated_request(
                method='DELETE',
                endpoint=endpoint,
                headers=headers,
                params=params,
            )
            try:
                data = response.json()
            except ValueError:
                data = {'detail': response.text}
            return {'status_code': response.status_code, 'data': data}
        except (WMSAuthenticationError, WMSAPIError) as exc:
            logger.error('WMS API error cancelling order %s: %s', order_reference, exc)
            raise
