"""
WMS Service Module

This module provides a unified interface for WMS operations including
order management, returns, and inventory operations.
"""

from .return_service import process_order_return_to_wms, WMSReturnService
from .order_service import WMSOrderService
from .inventory_service import WMSInventoryService
from .invoice_service import WMSInvoiceService

# Export the main function for easy import
__all__ = [
    'process_order_return_to_wms',
    'WMSReturnService',
    'WMSOrderService', 
    'WMSInventoryService',
    'WMSInvoiceService'
]
