from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from integrations.tasks.invoice_pdf import process_invoice_task
from integrations.models import WMSIntegration

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('invoice_callback')


class InvoiceCallbackAPIView(APIView):
    """
    API endpoint to receive invoice creation callbacks and process them asynchronously.
    """

    def _validate_integration(self, wms_integration_name):
        """
        Validate that the WMS integration exists and is active.
        Args:
            wms_integration_name (str): Name of the WMS integration
        Returns:
            WMSIntegration: The validated WMS integration object
        Raises:
            ValueError: If the integration is not found or is inactive
        """
        try:
            wms_integration = WMSIntegration.objects.get(name=wms_integration_name, is_active=True)
            return wms_integration
        except WMSIntegration.DoesNotExist:
            raise ValueError(f"WMS integration '{wms_integration_name}' not found or inactive")

    def post(self, request, wms_integration_name):
        """
        Handle invoice creation callback for a specific WMS integration.

        URL: /api/potions/integrations/invoice/callback/{wms_integration_name}/

        Args:
            wms_integration_name (str): Name of the WMS integration (e.g., 'stockone', 'default')

        Expected payload structure:
        {
            "data": {
                "order_reference": "ORDgdfdffs5",
                "order_status": "invoiced",
                "order_date": "2025-08-11 13:54:57",
                "customer_name": "Ravi Ranjan",
                "invoice_amount": 231.28,
                ...
            },
            "warehouse": "ROZANA_TEST_WH1"
        }
        """
        try:
            # First, validate that the WMS integration exists
            wms_integration = self._validate_integration(wms_integration_name)
            logger.info(f"Using WMS integration: {wms_integration.name} (ID: {wms_integration.id})")

            payload = request.data

            # Extract required data from callback
            data = payload.get('data', {})
            warehouse = payload.get('warehouse')

            # Validate required fields
            if not data:
                return Response({
                    'error': 'Missing data field in payload'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not warehouse:
                return Response({
                    'error': 'Missing warehouse field in payload'
                }, status=status.HTTP_400_BAD_REQUEST)

            order_reference = data.get('order_reference')
            order_status = data.get('order_status')

            if not order_reference:
                return Response({
                    'error': 'Missing order_reference in data'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if order status is 'invoiced'
            if order_status != 'invoiced':
                logger.info(f"Ignoring callback for order {order_reference} with status: {order_status}")
                return Response({
                    'message': f'Order status is {order_status}, not invoiced. Ignoring callback.',
                    'order_reference': order_reference,
                    'status': order_status
                }, status=status.HTTP_200_OK)

            # Extract invoice date
            invoice_date_str = data.get('order_date') or data.get('shipment_date')
            logger.info(f"Received invoice callback for order: {order_reference}, warehouse: {warehouse}, WMS integration: {wms_integration_name}")

            # Trigger async processing with apply_async for better control
            task = process_invoice_task.apply_async(
                args=[
                    order_reference,
                    warehouse,
                    invoice_date_str,
                    wms_integration_name
                ],
                countdown=1,  # Start processing after 1 seconds
                retry=True,
                retry_policy={
                    'max_retries': 3,
                    'interval_start': 60,
                    'interval_step': 60,
                    'interval_max': 300,
                }
            )

            return Response({
                'message': 'Invoice callback received and processing started',
                'order_reference': order_reference,
                'warehouse': warehouse,
                'task_id': task.id,
                'status': 'processing_started'
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"Error processing invoice callback: {str(e)}")
            return Response({
                'error': 'Internal server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class InvoiceStatusAPIView(APIView):
    """
    API endpoint to check the status of invoice processing.
    """
    
    def get(self, request, task_id):
        """
        Get the status of an invoice processing task.
        
        Args:
            task_id (str): Celery task ID
        """
        try:
            from celery.result import AsyncResult
            
            # Get task result
            task_result = AsyncResult(task_id)
            
            response_data = {
                'task_id': task_id,
                'status': task_result.status,
            }
            
            if task_result.ready():
                if task_result.successful():
                    response_data.update(task_result.result)
                else:
                    response_data['error'] = str(task_result.result)
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error checking task status: {str(e)}")
            return Response({
                'error': 'Internal server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
