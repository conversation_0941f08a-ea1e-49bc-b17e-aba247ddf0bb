from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
import datetime
import os


@method_decorator(csrf_exempt, name='dispatch')
class HealthCheckAPIView(APIView):
    """
    Simple health check endpoint for the Potions service
    Returns service status and timestamp
    """
    
    def get(self, request):
        """
        Return simple health status with timestamp
        """
        uptime = datetime.datetime.now() - datetime.datetime.fromtimestamp(os.path.getmtime(__file__))
        return Response({
            "service": "potions",
            "status": "healthy",
            "uptime": uptime.total_seconds()
        }, status=status.HTTP_200_OK)
