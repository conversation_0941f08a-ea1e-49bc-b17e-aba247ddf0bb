from potions.logging.utils import get_app_logger
import json
from django.http import JsonResponse
from django.utils import timezone
from oauth2_provider.models import AccessToken

logger = get_app_logger('auth_middleware')


class OAuth2IntegrationMiddleware:
    """
    Modern Django OAuth2 middleware for integration APIs.
    
    This middleware handles OAuth2 authentication for all integration API endpoints
    using the current Django middleware format (callable class).
    """

    # Define which URL patterns require OAuth2 authentication
    PROTECTED_PATHS = [
        '/api/potions/integrations/',
    ]

    # Define which URL patterns should be excluded from OAuth2 authentication
    EXCLUDED_PATHS = [
        '/api/potions/integrations/webhooks/',        # Webhooks use
        '/api/potions/integrations/invoice/callback/', # Invoice callbacks use
    ]

    def __init__(self, get_response):
        """
        Initialize the middleware.
        
        Args:
            get_response: The next middleware or view in the chain
        """
        self.get_response = get_response

    def __call__(self, request):
        """
        Process the request through the middleware.

        Args:
            request: The HTTP request object

        Returns:
            HTTP response from the next middleware/view or authentication error
        """
        # Check if this request path requires OAuth2 authentication
        if self._requires_auth(request.path):
            # Extract and validate OAuth2 token
            auth_result = self._validate_oauth2_token(request)

            if not auth_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': 'Authentication required',
                    'details': auth_result['error']
                }, status=401)

            # Set authenticated user and token in request
            request.user = auth_result['user']
            request.auth = auth_result['token']

            # Log successful authentication
            logger.info(f"OAuth2 authentication successful for user: {request.user}")

        # Continue to the next middleware or view
        response = self.get_response(request)
        return response

    def _requires_auth(self, path):
        """
        Check if the given path requires OAuth2 authentication.
        Args:
            path: The request path to check
        Returns:
            bool: True if authentication is required, False otherwise
        """
        # Check if path is explicitly excluded
        if any(path.startswith(excluded_path) for excluded_path in self.EXCLUDED_PATHS):
            return False

        # Check if path is in protected paths
        return any(path.startswith(protected_path) for protected_path in self.PROTECTED_PATHS)

    def _validate_oauth2_token(self, request):
        """
        Validate OAuth2 Bearer token from the Authorization header.
        Args:
            request: The HTTP request object
        Returns:
            dict: Authentication result with 'valid', 'user', 'token', and 'error' keys
        """
        # Check if Authorization header is present
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            return {
                'valid': False,
                'error': 'Authorization header missing',
                'user': None,
                'token': None
            }

        # Check if it's a Bearer token
        if not auth_header.startswith('Bearer '):
            return {
                'valid': False,
                'error': 'Invalid authorization header format. Expected: Bearer <token>',
                'user': None,
                'token': None
            }

        # Extract token from header
        try:
            token = auth_header.split(' ')[1]
        except IndexError:
            return {
                'valid': False,
                'error': 'Invalid Bearer token format',
                'user': None,
                'token': None
            }

        # Validate the OAuth2 token
        try:
            access_token = AccessToken.objects.get(token=token)

            # Check if token is expired
            if access_token.expires < timezone.now():
                logger.warning(f"Expired OAuth2 token attempted: {token[:10]}...")
                return {
                    'valid': False,
                    'error': 'Access token has expired',
                    'user': None,
                    'token': None
                }

            # Check if token is active (not revoked)
            if not hasattr(access_token, 'is_valid') or not access_token.is_valid():
                logger.warning(f"Invalid OAuth2 token attempted: {token[:10]}...")
                return {
                    'valid': False,
                    'error': 'Access token is not valid',
                    'user': None,
                    'token': None
                }

            # Token is valid
            logger.debug(f"Valid OAuth2 token for user: {access_token.user}")
            return {
                'valid': True,
                'error': None,
                'user': access_token.user,
                'token': access_token
            }

        except AccessToken.DoesNotExist:
            logger.warning(f"Non-existent OAuth2 token attempted: {token[:10]}...")
            return {
                'valid': False,
                'error': 'Invalid access token',
                'user': None,
                'token': None
            }
        except Exception as e:
            logger.error(f"OAuth2 token validation error: {str(e)}")
            return {
                'valid': False,
                'error': 'Token validation failed',
                'user': None,
                'token': None
            }

    def process_exception(self, request, exception):
        """
        Handle exceptions that occur during request processing.

        Args:
            request: The HTTP request object
            exception: The exception that occurred

        Returns:
            None to let Django handle the exception normally
        """
        # Log authentication-related exceptions
        if hasattr(request, 'path') and self._requires_auth(request.path):
            logger.error(f"Exception in OAuth2 middleware for path {request.path}: {str(exception)}")

        # Return None to let Django handle the exception normally
        return None