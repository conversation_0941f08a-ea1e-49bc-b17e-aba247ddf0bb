"""
Audit Middleware for Rozana Potions
Request logging and audit tracking
"""
import json
import socket
import time
from datetime import datetime
from django.http import HttpRequest, HttpResponse
from django.http.request import RawPostDataException
from django.conf import settings

from potions.logging.utils import get_app_logger, init_audit_logger
from potions.logging.config import LoggingConfig
from potions.middlewares.request_context import create_request_id, request_context, clear_request_context


class AuditMiddleware:
    """Combined audit and request logging middleware"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = get_app_logger()
        self.exclude_audit_paths = ['/admin/', '/health/']
        self.hostname = socket.gethostname()
        self.app_name = getattr(settings, 'APP_NAME', 'rozana-potions')
        self.version = getattr(settings, 'APP_VERSION', '1.0.0')
    
    def __call__(self, request: HttpRequest):
        try:
            # Generate UUID and store in thread-local
            request.request_id = create_request_id()
            request.start_time = time.time()
            request.timestamp = datetime.now().isoformat()

            # Cache request body early to avoid RawPostDataException later
            try:
                request._cached_body = request.body if hasattr(request, 'body') else b''
            except (RawPostDataException, Exception):
                request._cached_body = b''

            # Store module name in thread-local
            request_context.module_name = self._get_module_name(request)


            # Log request start
            self.logger.info(f"Request: {request.method} {request.path}")

            # Check if audit is needed (all paths except excluded ones) and audit logging is enabled
            should_audit = (LoggingConfig.AUDIT_LOGGING_ENABLED and not any(request.path.startswith(path) for path in self.exclude_audit_paths))
            if should_audit:
                request.should_audit = True

            try:
                # Get response
                response = self.get_response(request)

                # Calculate duration
                duration = (time.time() - request.start_time) * 1000

                # Log response
                self.logger.info(
                    f"Response: {request.method} {request.path} - {response.status_code} ({duration:.0f}ms)"
                )

                # Log audit event if needed
                if getattr(request, 'should_audit', False):
                    audit_data = self._build_audit_data(request, response, duration)
                    # Get appropriate audit logger based on method
                    audit_logger = self._get_audit_logger_for_method(request.method)
                    audit_logger.info("Audit log", extra=audit_data)

                return response

            except Exception as exception:
                # Log exception
                duration = (time.time() - request.start_time) * 1000
                self.logger.error(
                    f"Exception: {request.method} {request.path} - {exception.__class__.__name__} ({duration:.0f}ms)",
                    exc_info=True
                )

                # Log audit for exceptions too if needed
                if getattr(request, 'should_audit', False):
                    # Create mock response for exception case
                    error_response = HttpResponse(status=500)
                    audit_data = self._build_audit_data(request, error_response, duration)
                    audit_data['exception'] = exception.__class__.__name__
                    # Get appropriate audit logger based on method
                    audit_logger = self._get_audit_logger_for_method(request.method)
                    audit_logger.info("Audit log (exception)", extra=audit_data)

                raise

            finally:
                # Clear request context from thread-local storage
                clear_request_context()

        except Exception as middleware_exception:
            # Final safety net - log middleware failure but continue with basic response
            try:
                self.logger.error(f"Middleware failure: {middleware_exception.__class__.__name__}", exc_info=True)
            except:
                # Even logging failed - use print as absolute last resort
                print(f"Critical middleware failure: {middleware_exception}")
            
            # Return basic response to prevent complete failure
            try:
                return self.get_response(request)
            except:
                # If even get_response fails, return minimal HTTP response
                return HttpResponse("Internal Server Error", status=500)
    
    def _get_audit_logger_for_method(self, method):
        """Get appropriate audit logger based on HTTP method"""
        if method == 'GET':
            stream_name = LoggingConfig.AUDIT_LOGS_GET_STREAM_NAME
        else:
            stream_name = LoggingConfig.AUDIT_LOGS_STREAM_NAME
        
        return init_audit_logger(stream_name)
    
    def _build_audit_data(self, request: HttpRequest, response: HttpResponse, duration: float):
        """Build complete audit data with all required fields"""
        # Get request body from cached data to avoid RawPostDataException
        try:
            cached_body = getattr(request, '_cached_body', b'')
            if cached_body:
                if request.content_type == 'application/json':
                    body_data = json.loads(cached_body.decode('utf-8'))
                else:
                    body_data = cached_body.decode('utf-8')
            else:
                body_data = {}
        except (json.JSONDecodeError, UnicodeDecodeError, AttributeError):
            body_data = {}
        
        # Get response content
        try:
            if hasattr(response, 'content'):
                if response.get('Content-Type', '').startswith('application/json'):
                    response_data = json.loads(response.content.decode('utf-8'))
                else:
                    response_data = response.content.decode('utf-8')[:1000]  # Limit size
            else:
                response_data = ""
        except (json.JSONDecodeError, UnicodeDecodeError, AttributeError):
            response_data = ""
        
        # Build request JSON structure
        request_json = {
            "GET": dict(request.GET),
            "POST": dict(request.POST),
            "BODY": body_data,
            "HEADERS": {
                key: value for key, value in request.META.items() 
                if key.startswith('HTTP_') or key in [
                    'CONTENT_TYPE', 'CONTENT_LENGTH', 'REQUEST_METHOD', 
                    'PATH_INFO', 'QUERY_STRING', 'REMOTE_ADDR'
                ]
            }
        }
        
        # Calculate response size
        size_in_bytes = len(response.content) if hasattr(response, 'content') else 0
        
        # Get module name (Django app)
        module_name = self._get_module_name(request.path)
        
        return {
            'duration': round(duration, 2),
            'header_referer': request.META.get('HTTP_REFERER', ''),
            'hostname': self.hostname,
            'app_name': self.app_name,
            'module_name': module_name,
            'request': request_json,
            'request_id': request.request_id,
            'request_method': request.method,
            'request_path': request.path,
            'response': response_data,
            'size_in_bytes': size_in_bytes,
            'status_code': response.status_code,
            'timestamp': request.timestamp,
            'version': self.version
        }
    
    def _get_module_name(self, request):
        """Extract Django app namespace from request resolver match"""
        if hasattr(request, 'resolver_match') and request.resolver_match:
            return request.resolver_match.namespace
        return None
    
