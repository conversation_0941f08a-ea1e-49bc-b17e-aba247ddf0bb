"""
Request Context Management for Rozana Potions
Thread-local storage for request correlation and business context
"""
import threading
import uuid


class RequestContextLocal(threading.local):
    """Thread-local storage for request context"""
    def __init__(self):
        self.request_id = None
        self.module_name = None
        self.url_name = None
        self.facility_id = None
        self.order_id = None


# Global thread-local instance
request_context = RequestContextLocal()


def create_request_id():
    """Generate a new request ID and store in thread-local storage"""
    request_uuid = str(uuid.uuid4())
    request_context.request_id = request_uuid
    return request_uuid


def get_current_request_id():
    """Get current request ID from thread-local storage"""
    return getattr(request_context, 'request_id', None)


def clear_request_context():
    """Clear all request context from thread-local storage"""
    request_context.request_id = None
    request_context.module_name = None
    request_context.url_name = None
    request_context.facility_id = None
    request_context.order_id = None
