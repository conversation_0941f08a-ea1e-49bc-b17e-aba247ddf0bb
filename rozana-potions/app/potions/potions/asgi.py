"""
ASGI config for potions project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'potions.settings')

# Initialize Django ASGI application early to ensure the AppRegistry is populated
# before importing other modules that may use Django models
django_asgi_app = get_asgi_application()

# Import after Django is initialized
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from django.urls import re_path
from scripts.consumers import InventorySyncConsumer

websocket_urlpatterns = [
    re_path(r'ws/inventory-sync/(?P<task_id>[\w-]+)/$', InventorySyncConsumer.as_asgi()),
]

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AuthMiddlewareStack(
        URLRouter(websocket_urlpatterns)
    ),
})
