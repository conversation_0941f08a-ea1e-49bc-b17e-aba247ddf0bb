"""
Simplified Log Processors for Rozana Potions
Basic process-based logging without fancy features
"""
import multiprocessing
import time
import signal
from datetime import datetime

from potions.logging.config import LoggingConfig
from potions.logging.handlers import FireHoseHandler


class LogProcessor(multiprocessing.Process):
    """Basic log processor process"""
    
    def __init__(self, process_id, input_queue, shutdown_event):
        super().__init__(name=f"LogProcessor-{process_id}")
        self.process_id = process_id
        self.input_queue = input_queue
        self.shutdown_event = shutdown_event
        self.handlers = {}
    
    def run(self):
        """Main processor loop"""
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        try:
            while not self.shutdown_event.is_set():
                try:
                    work_item = self.input_queue.get(timeout=1.0)
                    if work_item is None:
                        break
                    self._process_work_item(work_item)
                except multiprocessing.queues.Empty:
                    continue
                except Exception as e:
                    print(f"Error in processor {self.process_id}: {e}")
        except KeyboardInterrupt:
            pass
        finally:
            self._cleanup()
    
    def _process_work_item(self, work_item):
        """Process a work item"""
        stream_name = work_item['stream_name']
        actions = work_item['actions']
        
        if stream_name not in self.handlers:
            self.handlers[stream_name] = FireHoseHandler(stream_name)
        
        try:
            self.handlers[stream_name].bulk_insert(actions)
        except Exception as e:
            print(f"Failed to process batch for {stream_name}: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.shutdown_event.set()
    
    def _cleanup(self):
        """Cleanup resources"""
        for handler in self.handlers.values():
            if hasattr(handler, 'close'):
                handler.close()


class ProcessPool:
    """Basic process pool for log processors"""
    
    def __init__(self, pool_size=None):
        self.pool_size = pool_size or LoggingConfig.LOG_PROCESSOR_POOL_SIZE
        self.max_queue_size = LoggingConfig.MAX_QUEUE_SIZE
        
        self.work_queue = multiprocessing.Queue(maxsize=self.max_queue_size)
        self.shutdown_event = multiprocessing.Event()
        self.processes = []
        self.is_running = False
    
    def start(self):
        """Start the process pool"""
        if self.is_running:
            return
        
        for i in range(self.pool_size):
            process = LogProcessor(i, self.work_queue, self.shutdown_event)
            process.start()
            self.processes.append(process)
        
        self.is_running = True
    
    def stop(self):
        """Stop the process pool"""
        if not self.is_running:
            return
        
        self.shutdown_event.set()
        
        # Send poison pills
        for _ in range(len(self.processes)):
            try:
                self.work_queue.put(None, timeout=1.0)
            except:
                pass
        
        # Wait for processes
        for process in self.processes:
            process.join(timeout=5.0)
            if process.is_alive():
                process.terminate()
                process.join(timeout=2.0)
        
        self.processes.clear()
        self.is_running = False
    
    def submit_work(self, stream_name, actions):
        """Submit work to the pool"""
        if not self.is_running:
            self.start()
        
        work_item = {
            'stream_name': stream_name,
            'actions': actions,
            'timestamp': time.time()
        }
        
        try:
            self.work_queue.put(work_item, timeout=1.0)
            return True
        except:
            return False


# Global process pool
_process_pool = None

def get_process_pool():
    """Get global process pool"""
    global _process_pool
    if _process_pool is None:
        _process_pool = ProcessPool()
    return _process_pool

def submit_logs(stream_name, actions):
    """Submit logs to process pool"""
    pool = get_process_pool()
    return pool.submit_work(stream_name, actions)

def shutdown_logging():
    """Shutdown logging system"""
    global _process_pool
    if _process_pool:
        _process_pool.stop()
        _process_pool = None
