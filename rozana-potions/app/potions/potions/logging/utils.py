"""
Simple Logging Utilities for Rozana Potions
Basic setup functions only
"""
import logging
import atexit

from potions.logging.config import LoggingConfig
from potions.logging.handlers import get_app_handler, get_audit_handler
from potions.logging.filters import Request<PERSON>ontextFilter, BusinessContextFilter
from potions.logging.processors import get_process_pool, shutdown_logging


def setup_app_logging(logger_name):
    """Set up application logging"""
    logger = logging.getLogger(logger_name)
    logger.handlers.clear()
    
    handler = get_app_handler()
    handler.addFilter(RequestContextFilter())
    handler.addFilter(BusinessContextFilter())
    
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False
    
    return logger


def setup_audit_logging(logger_name, ):
    """Set up audit logging"""
    logger = logging.getLogger(logger_name)
    logger.handlers.clear()
    
    handler = get_audit_handler()
    handler.addFilter(RequestContextFilter())
    
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    logger.propagate = False
    
    return logger


def get_logger(log_type='app', logger_name='potions'):
    """Get logger with specified type and name"""
    full_name = f"{logger_name}.{log_type}" if log_type != 'app' else logger_name
    
    logger = logging.getLogger(full_name)
    if logger.handlers:
        return logger
    
    if log_type == 'audit':
        return setup_audit_logging(full_name)
    else:
        return setup_app_logging(full_name)


def get_app_logger(name=None):
    """Get application logger - centralized when Firehose enabled, individual files when disabled"""
    if name:
        logger = logging.getLogger(name)
        if not logger.handlers:
            logger.handlers.clear()
            
            if LoggingConfig.FIREHOSE_ENABLED:
                # Use centralized global handler for Firehose
                handler = get_app_handler()
            else:
                # Use individual file handler for local development
                from potions.logging.handlers import get_local_file_handler
                handler = get_local_file_handler(f'{name.replace(".", "_")}')
            
            handler.addFilter(RequestContextFilter())
            handler.addFilter(BusinessContextFilter())
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            logger.propagate = False
        return logger
    else:
        # Default app logger
        return get_logger('app', 'potions')


def get_audit_logger():
    """Get audit logger"""
    return get_logger('audit', 'potions')


def init_app_logger(module_name):
    """Initialize and get centralized app logger for any module - all logs go to app-logs stream"""
    return get_app_logger(module_name)


def log_audit_event(message="Audit event", **context):
    """Log basic audit event"""
    logger = get_logger('audit', 'potions')
    
    logger.info(message, extra=context)


def initialize_logging():
    """Initialize logging system"""
    try:
        is_valid, message = LoggingConfig.is_valid_config()
        if not is_valid:
            print(f"Warning: {message}")
        
        if LoggingConfig.ASYNC_LOGGING:
            pool = get_process_pool()
            pool.start()
            atexit.register(shutdown_logging)
        
        print("Logging system initialized")
        
    except Exception as e:
        print(f"Failed to initialize logging: {e}")
        raise


def init_audit_logger(stream_name=None):
    """Initialize and get audit logger for centralized audit logging
    
    Args:
        stream_name (str, optional): Firehose stream name for audit logs
                                   If None, returns default audit logger
    
    Returns:
        Logger: Configured audit logger with appropriate handler for the stream
    """
    if stream_name:
        # Create stream-specific logger name
        logger_name = f"potions.audit.{stream_name.replace('-', '_')}"
        logger = logging.getLogger(logger_name)
        
        if not logger.handlers:
            logger.handlers.clear()
            
            # Get handler for specific stream
            from potions.logging.handlers import AuditLogsMemoryHandler, get_local_file_handler
            from potions.logging.config import LoggingConfig
            
            if LoggingConfig.FIREHOSE_ENABLED:
                handler = AuditLogsMemoryHandler(stream_name)
            else:
                # Use stream name as file suffix for local development
                handler = get_local_file_handler(f"audit_{stream_name.replace('-', '_')}")
            
            handler.addFilter(RequestContextFilter())
            
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            logger.propagate = False
        
        return logger
    else:
        # Return default audit logger
        return get_audit_logger()


# Common loggers - cached instances
_app_logger = None
_audit_logger = None

def get_default_app_logger():
    """Get default application logger (cached)"""
    global _app_logger
    if _app_logger is None:
        _app_logger = get_logger('app')
    return _app_logger

def get_default_audit_logger():
    """Get default audit logger (cached)"""
    global _audit_logger
    if _audit_logger is None:
        _audit_logger = get_logger('audit', 'audit')
    return _audit_logger
