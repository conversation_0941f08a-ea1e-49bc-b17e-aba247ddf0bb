"""
Simplified Logging Configuration for Rozana Potions
Only firehose mode, essential features only
"""
import os
from django.conf import settings


class LoggingConfig:
    """Simplified logging configuration - firehose only"""

    # Core settings
    ASYNC_LOGGING = os.environ.get('ASYNC_LOGGING', 'FALSE').upper() == 'TRUE'
    BATCH_PROCESSING = os.environ.get('BATCH_PROCESSING', 'FALSE').upper() == 'TRUE'
    FIREHOSE_ENABLED = os.environ.get('FIREHOSE_ENABLED', 'FALSE').upper() == 'TRUE'
    AUDIT_LOGGING_ENABLED = os.environ.get('AUDIT_LOGGING_ENABLED', 'FALSE').upper() == 'TRUE'

    # Stream Names
    APP_LOGS_STREAM_NAME = os.getenv('APP_LOGS_STREAM_NAME', '')
    AUDIT_LOGS_STREAM_NAME = os.getenv('AUDIT_LOGS_STREAM_NAME', '')
    AUDIT_LOGS_GET_STREAM_NAME = os.getenv('AUDIT_LOGS_GET_STREAM_NAME', '')
    LOG_BUFFER_TIMEOUT = int(os.environ.get('LOG_BUFFER_TIMEOUT', 600))

    # Process management
    # Buffer sizes
    APP_LOGS_CAPACITY = int(os.environ.get('APP_LOGS_CAPACITY', 50))
    AUDIT_LOGS_CAPACITY = int(os.environ.get('AUDIT_LOGS_CAPACITY', 50))
    AUDIT_LOGS_GET_CAPACITY = int(os.environ.get('AUDIT_LOGS_GET_CAPACITY', 50))
    LOG_PROCESSOR_POOL_SIZE = int(os.environ.get('LOG_PROCESSOR_POOL_SIZE', 2))
    MAX_QUEUE_SIZE = int(os.environ.get('MAX_QUEUE_SIZE', 10))

    # Firehose settings (required)
    FIREHOSE_REGION_NAME = os.environ.get('FIREHOSE_REGION_NAME', 'ap-south-1')
    FIREHOSE_ACCESS_KEY_ID = os.environ.get('FIREHOSE_ACCESS_KEY_ID', '')
    FIREHOSE_SECRET_ACCESS_KEY = os.environ.get('FIREHOSE_SECRET_ACCESS_KEY', '')
    FIREHOSE_RETRY_COUNT = int(os.environ.get('FIREHOSE_RETRY_COUNT', 3))
    FIREHOSE_RETRY_DELAY = int(os.environ.get('FIREHOSE_RETRY_DELAY', 1))
    
    @classmethod
    def get_firehose_stream_name(cls, log_type='app'):
        """Generate Firehose stream name"""
        environment = getattr(settings, 'APPLICATION_ENVIRONMENT', 'UAT').lower()
        return f"rozana-potions-{environment}-{log_type}-logs"
    
    @classmethod
    def is_valid_config(cls):
        """Validate configuration - only check Firehose when enabled"""
        if cls.FIREHOSE_ENABLED:
            if not cls.FIREHOSE_ACCESS_KEY_ID or not cls.FIREHOSE_SECRET_ACCESS_KEY:
                return False, "Firehose credentials not configured"
        return True, "Configuration is valid"
