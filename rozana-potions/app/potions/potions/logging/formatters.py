"""
Simplified JSON Formatters for Rozana Potions Logging
Basic formatters - no fancy features
"""
import json
import logging
from datetime import datetime
from django.conf import settings

class BaseJSONFormatter(logging.Formatter):
    """Basic JSON formatter"""
    
    def __init__(self):
        super().__init__()
        self.application_environment = getattr(settings, 'APPLICATION_ENVIRONMENT', 'UAT')
    
    def format(self, record):
        """Convert log record to JSON format"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line_number': record.lineno,
            'environment': self.application_environment,
            'service': 'rozana-potions'
        }
        
        # Add exception if present
        if record.exc_info:
            log_entry['exception'] = str(record.exc_info[1])
        
        # Add extra fields from record
        self.add_extra_fields(log_entry, record)
        
        return json.dumps(log_entry, ensure_ascii=False, default=str)
    
    def add_extra_fields(self, log_entry, record):
        """Add extra fields - to be overridden"""
        pass


class AppLogsJSONFormatter(BaseJSONFormatter):
    """Application logs formatter"""
    
    def add_extra_fields(self, log_entry, record):
        """Add basic application fields - always present with defaults"""
        log_entry['request_id'] = getattr(record, 'request_id', '')
        log_entry['user_id'] = getattr(record, 'user_id', '')
        log_entry['order_id'] = getattr(record, 'order_id', '')
        log_entry['facility_id'] = getattr(record, 'facility_id', '')


class AuditLogsJSONFormatter(BaseJSONFormatter):
    """Simple audit logs formatter"""
    
    def add_extra_fields(self, log_entry, record):
        """Add basic audit fields - always present with defaults"""
        # Standard audit fields
        log_entry['user_id'] = getattr(record, 'user_id', '')
        log_entry['request_id'] = getattr(record, 'request_id', '')
 
        # Extended audit fields from middleware
        log_entry['duration'] = getattr(record, 'duration', 0.0)
        log_entry['header_referer'] = getattr(record, 'header_referer', '')
        log_entry['hostname'] = getattr(record, 'hostname', '')
        log_entry['app_name'] = getattr(record, 'app_name', '')
        log_entry['module_name'] = getattr(record, 'module_name', '')
        
        # Convert complex objects to JSON strings
        request_data = getattr(record, 'request', {})
        response_data = getattr(record, 'response', '')
        
        log_entry['request'] = json.dumps(request_data, ensure_ascii=False, default=str) if request_data else ''
        log_entry['response'] = json.dumps(response_data, ensure_ascii=False, default=str) if response_data else ''
        
        log_entry['request_method'] = getattr(record, 'request_method', '')
        log_entry['request_path'] = getattr(record, 'request_path', '')
        log_entry['size_in_bytes'] = getattr(record, 'size_in_bytes', 0)
        log_entry['status_code'] = getattr(record, 'status_code', 0)
        log_entry['version'] = getattr(record, 'version', '')
