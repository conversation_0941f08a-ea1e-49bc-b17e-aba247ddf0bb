"""
Simplified Logging Handlers for Rozana Potions
Firehose only, no fancy features
"""
import logging
import time
from logging.handlers import MemoryHandler

import boto3
from botocore.config import Config

from potions.logging.config import LoggingConfig
from potions.logging.formatters import App<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuditLogsJSONFormatter


class FireHoseHandler(logging.Handler):
    """Basic Firehose handler with compression enabled by default"""

    def __init__(self, stream_name):
        super().__init__()
        self.stream_name = stream_name
        self.client = self._create_client()
        self.retry_count = LoggingConfig.FIREHOSE_RETRY_COUNT
        self.retry_delay = LoggingConfig.FIREHOSE_RETRY_DELAY

    def _create_client(self):
        """Create Firehose client"""
        return boto3.client(
            "firehose",
            region_name=LoggingConfig.FIREHOSE_REGION_NAME,
            aws_access_key_id=LoggingConfig.FIREHOSE_ACCESS_KEY_ID,
            aws_secret_access_key=LoggingConfig.FIREHOSE_SECRET_ACCESS_KEY,
            config=Config(
                connect_timeout=10,
                read_timeout=30,
                retries={'max_attempts': 2}
            )
        )

    def bulk_insert(self, actions):
        """Send records to Firehose"""
        if not actions:
            return True
        
        print(f"FireHose: Attempting to send {len(actions)} records to stream '{self.stream_name}'")
        
        for attempt in range(self.retry_count):
            try:
                response = self.client.put_record_batch(
                    DeliveryStreamName=self.stream_name,
                    Records=actions
                )
                
                failed_count = response.get("FailedPutCount", 0)
                success_count = len(actions) - failed_count
                
                if failed_count == 0:
                    print(f"FireHose: Successfully sent {success_count}/{len(actions)} records to '{self.stream_name}'")
                    return True
                else:
                    print(f"FireHose: Partial success - {success_count} sent, {failed_count} failed on attempt {attempt + 1}")
                
                # Retry failed records
                if attempt < self.retry_count - 1:
                    print(f"FireHose: Retrying in {self.retry_delay * (2 ** attempt)} seconds...")
                    time.sleep(self.retry_delay * (2 ** attempt))
                
            except Exception as e:
                print(f"FireHose: Attempt {attempt + 1} failed with error: {e}")
                if attempt < self.retry_count - 1:
                    print(f"FireHose: Retrying in {self.retry_delay * (2 ** attempt)} seconds...")
                    time.sleep(self.retry_delay * (2 ** attempt))
                else:
                    print(f"FireHose Error: Failed to send records after {self.retry_count} attempts: {e}")
                    return False
        
        print(f"FireHose: Failed to send records to '{self.stream_name}' after all retry attempts")
        return False


class SimpleMemoryHandler(MemoryHandler):
    """Simplified memory handler"""
    
    def __init__(self, capacity, target_handler, stream_name):
        super().__init__(capacity=capacity, target=target_handler)
        self.stream_name = stream_name
        self.buffer_timeout = LoggingConfig.LOG_BUFFER_TIMEOUT
        self.last_flush = time.time()

    def emit(self, record):
        """Add record to buffer"""
        super().emit(record)
        
        # Check if we need to flush
        current_time = time.time()
        if current_time - self.last_flush >= self.buffer_timeout:
            print(f"Buffer: Timeout reached ({self.buffer_timeout}s), flushing '{self.stream_name}' buffer")
            self.flush()
        elif len(self.buffer) >= self.capacity:
            print(f"Buffer: Capacity reached ({self.capacity}), flushing '{self.stream_name}' buffer")
            self.flush()

    def flush(self):
        """Send buffered records to Firehose"""
        self.acquire()
        try:
            if self.target and self.buffer:
                buffer_size = len(self.buffer)
                print(f"Flush: Processing {buffer_size} records from '{self.stream_name}' buffer")
                
                actions = []
                for record in self.buffer:
                    log_entry = self.format(record)
                    actions.append({"Data": log_entry})
                
                if actions:
                    success = self.target.bulk_insert(actions)
                    if success:
                        print(f"Flush: Successfully flushed {len(actions)} records from '{self.stream_name}'")
                    else:
                        print(f"Flush: Failed to flush records from '{self.stream_name}'")
                
                self.buffer.clear()
                self.last_flush = time.time()
                print(f"Flush: Buffer cleared for '{self.stream_name}'")
            else:
                print(f"Flush: Nothing to flush for '{self.stream_name}' (empty buffer or no target)")
        finally:
            self.release()


class AppLogsMemoryHandler(SimpleMemoryHandler):
    """Application logs handler"""
    
    def __init__(self, stream_name=LoggingConfig.APP_LOGS_STREAM_NAME):
        target_handler = FireHoseHandler(stream_name)
        
        super().__init__(
            capacity=LoggingConfig.APP_LOGS_CAPACITY,
            target_handler=target_handler,
            stream_name=stream_name
        )
        
        formatter = AppLogsJSONFormatter()
        self.setFormatter(formatter)
        target_handler.setFormatter(formatter)


class AuditLogsMemoryHandler(SimpleMemoryHandler):
    """Audit logs handler for non-GET methods (POST/PUT/DELETE/PATCH)"""
    
    def __init__(self, stream_name=LoggingConfig.AUDIT_LOGS_STREAM_NAME):
        target_handler = FireHoseHandler(stream_name)
        
        super().__init__(
            capacity=LoggingConfig.AUDIT_LOGS_CAPACITY,
            target_handler=target_handler,
            stream_name=stream_name
        )
        
        formatter = AuditLogsJSONFormatter()
        self.setFormatter(formatter)
        target_handler.setFormatter(formatter)


# Global handlers
_handlers = {}

def get_local_file_handler(name='app'):
    """Get local file handler for development"""
    import logging
    handler = logging.FileHandler(f'logs/{name}.log')
    formatter = AppLogsJSONFormatter() if name == 'app' else AuditLogsJSONFormatter()
    handler.setFormatter(formatter)
    return handler


def get_app_handler():
    """Get application logs handler - Firehose or local file based on config"""
    if LoggingConfig.FIREHOSE_ENABLED:
        if 'app' not in _handlers:
            _handlers['app'] = AppLogsMemoryHandler(LoggingConfig.APP_LOGS_STREAM_NAME)
        return _handlers['app']
    else:
        # For local development, return individual file handler
        return get_local_file_handler('app')

def get_audit_handler(method=''):
    """Get appropriate audit logs handler based on HTTP method and config"""
    if LoggingConfig.FIREHOSE_ENABLED:
        # Centralized Firehose handlers
        if method == 'GET':
            if 'audit_get' not in _handlers:
                _handlers['audit_get'] = AuditLogsMemoryHandler(LoggingConfig.AUDIT_LOGS_GET_STREAM_NAME)
            return _handlers['audit_get']
        else:
            if 'audit_all' not in _handlers:
                _handlers['audit_all'] = AuditLogsMemoryHandler(LoggingConfig.AUDIT_LOGS_STREAM_NAME)
            return _handlers['audit_all']
    else:
        # Local development - use single audit_logs_backup.log for all audit logs
        return get_local_file_handler('audit_logs_backup')
