"""
Basic Logging Filters for Rozana Potions
Essential filters only
"""
import logging
import uuid
from potions.middlewares.request_context import request_context


class RequestContextFilter(logging.Filter):
    """Basic request context filter"""
    
    def filter(self, record):
        """Add basic request context"""
        # Use new request context system
        record.request_id = getattr(request_context, 'request_id', str(uuid.uuid4()))
        record.url = ''
        record.method = ''
        record.user_id = None
        
        return True


class BusinessContextFilter(logging.Filter):
    """Basic business context filter"""
    
    def filter(self, record):
        """Add basic business context"""
        # Use new request context system
        record.facility_id = getattr(request_context, 'facility_id', '')
        record.order_id = getattr(request_context, 'order_id', '')
        
        return True
