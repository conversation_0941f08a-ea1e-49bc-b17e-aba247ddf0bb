"""
Custom Uvicorn worker for Django ASGI applications.

This worker disables the lifespan protocol which can cause issues
with Django applications that don't implement lifespan handlers.
"""

from uvicorn.workers import UvicornWorker as BaseUvicornWorker


class UvicornWorker(BaseUvicornWorker):
    """
    Custom Uvicorn worker that disables lifespan protocol.
    
    This prevents ASGI lifespan protocol errors when using Django
    applications that don't implement lifespan event handlers.
    """
    CONFIG_KWARGS = {
        "lifespan": "off",
        "access_log": True,
        "use_colors": False,
    }
