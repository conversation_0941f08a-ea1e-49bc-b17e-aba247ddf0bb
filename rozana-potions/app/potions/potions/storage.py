from django.conf import settings
from storages.backends.s3boto3 import S3Boto3Storage
from django.core.files.storage import FileSystemStorage


class AWSStaticStorage(S3Boto3Storage):
    location = 'static'
    default_acl = 'public-read'


class AWSPublicMediaStorage(S3Boto3Storage):
    location = 'media'
    default_acl = 'public-read'
    file_overwrite = True  # True if files should be overwritten


class AWSPrivateMediaStorage(S3Boto3Storage):
    location = 'media'
    default_acl = 'private'
    file_overwrite = True  # True if files should be overwritten
    custom_domain = False
    
    def url(self, name, expire=300, parameters=None):
        """
        Returns a pre-signed upload URL for the given object key.
        :param name: The path/key for the object in S3 (e.g. "media/file.jpg")
        :param expiration: Time in seconds (default: 300s = 5 min)
        """
        params = {
            'Bucket': self.bucket_name,
            'Key': self.location + '/' + name,
        }
        parameters = parameters or {}
        if parameters.get('content_type'):
            params['ContentType'] = parameters['content_type']
        operation = parameters.pop('operation', '')
        client_method = 'get_object' 
        if operation == 'write':
            client_method = 'put_object'
        signed_url = self.connection.meta.client.generate_presigned_url(
            ClientMethod=client_method,
            Params=params,
            ExpiresIn=expire,
        )
        return signed_url


class StaticStorage:
    """Dynamically selects the appropriate storage backend for static files at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSStaticStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)


class PublicMediaStorage:
    """Dynamically selects the appropriate storage backend for public media files at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSPublicMediaStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)


class PrivateMediaStorage:
    """Dynamically selects the appropriate storage backend for private media files at runtime."""
    def __new__(cls, *args, **kwargs):
        if getattr(settings, 'USE_S3', False):
            return AWSPrivateMediaStorage(*args, **kwargs)
        else:
            return FileSystemStorage(*args, **kwargs)
