from django.contrib import admin
from .models import InventorySync, FacilityInventorySync

# Register your models here.

@admin.register(InventorySync)
class InventorySyncAdmin(admin.ModelAdmin):
    list_display = ('name', 'source_integration', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at', 'source_integration')
    search_fields = ('name', 'source_integration__name')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('source_integration',)
    filter_horizontal = ('target_lambda_integrations',)

    fieldsets = (
        ('Basic Configuration', {
            'fields': ('name', 'is_active')
        }),
        ('Source Integration', {
            'fields': ('source_integration',)
        }),
        ('Target Integrations', {
            'fields': ('target_lambda_integrations',),
            'description': 'Select multiple target systems to push inventory data to'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
        })
    )

@admin.register(FacilityInventorySync)
class FacilityInventorySyncAdmin(admin.ModelAdmin):
    list_display = ('facility', 'inventory_sync', 'is_active', 'force_sync', 'created_at')
    list_filter = ('is_active', 'created_at', 'facility', 'inventory_sync')
    search_fields = ('facility__name', 'inventory_sync__name')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('facility', 'inventory_sync')
    change_list_template = 'admin/scripts/facilityinventorysync/change_list.html'

    fieldsets = (
        ('Basic Information', {
            'fields': ('facility', 'inventory_sync', 'is_active', 'force_sync')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
        })
    )
    
    def changelist_view(self, request, extra_context=None):
        """Override changelist view to provide additional context for the template."""
        extra_context = extra_context or {}
        
        # Add any additional context needed for the sync functionality
        extra_context.update({
            'has_sync_permission': request.user.has_perm('scripts.change_facilityinventorysync'),
            'sync_api_url': '/scripts/api/start-inventory-sync/',
        })
        
        return super().changelist_view(request, extra_context=extra_context)
    
    def get_queryset(self, request):
        """Override queryset to include facility name for template access."""
        return super().get_queryset(request).select_related('facility', 'inventory_sync')

