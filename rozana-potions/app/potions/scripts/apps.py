from django.apps import AppConfig


class ScriptsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'scripts'
    
    def ready(self):
        """Import Celery tasks when Django app is ready to ensure they're registered."""
        try:
            # Import all task modules to register them with Celery
            import scripts.tasks.inventory
        except ImportError:
            pass
