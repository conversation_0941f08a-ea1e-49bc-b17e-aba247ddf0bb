from potions.logging.utils import get_app_logger
import time
import asyncio

from django.utils import timezone
from potions.celery import app as celery_app
from datetime import timedelta
from scripts.models import InventorySync
from integrations.services.wms.inventory_service import WMSInventoryService
from core.models import Facility
from scripts.models import FacilityInventorySync

from integrations.services.transformation.main import transform_wms_inventory_to_lambda_format
from integrations.services.lamda.lamda_wrapper import LambdaWrapper

from integrations.services.wms.auth_service import WMSAPIError
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync


logger = get_app_logger('inventory_sync')


def send_progress_update(task_id, current_page, total_pages, processed_items, total_items, facility_name, start_time):
    """Send progress update via WebSocket channel."""
    try:
        channel_layer = get_channel_layer()
        if not channel_layer:
            logger.warning("Channel layer not configured")
            return
            
        # Calculate progress percentage
        progress_percentage = (current_page / total_pages * 100) if total_pages > 0 else 0
        
        # Calculate ETA
        elapsed_time = time.time() - start_time
        if current_page > 0:
            avg_time_per_page = elapsed_time / current_page
            remaining_pages = total_pages - current_page
            eta_seconds = remaining_pages * avg_time_per_page
            eta_formatted = format_duration(eta_seconds)
        else:
            eta_formatted = "Calculating..."
        
        # Send progress update
        async_to_sync(channel_layer.group_send)(
            f'inventory_sync_{task_id}',
            {
                'type': 'sync_progress',
                'task_id': task_id,
                'current_page': current_page,
                'total_pages': total_pages,
                'processed_items': processed_items,
                'total_items': total_items,
                'facility_name': facility_name,
                'progress_percentage': round(progress_percentage, 1),
                'eta_formatted': eta_formatted,
                'status': 'in_progress',
                'timestamp': timezone.now().isoformat()
            }
        )
        logger.info(f"Sent progress update for task {task_id}: {current_page}/{total_pages} pages")
        print("Sent progress update for task {task_id}: {current_page}/{total_pages} pages")
    except Exception as e:
        print("Failed to send progress update: ", str(e))
        logger.error(f"Failed to send progress update: {str(e)}")


def send_completion_update(task_id, facility_name, total_processed, start_time, status='completed', message=None):
    """Send completion update via WebSocket channel."""
    try:
        channel_layer = get_channel_layer()
        if not channel_layer:
            logger.warning("Channel layer not configured")
            return
            
        duration = time.time() - start_time
        duration_formatted = format_duration(duration)
        
        async_to_sync(channel_layer.group_send)(
            f'inventory_sync_{task_id}',
            {
                'type': 'sync_complete',
                'task_id': task_id,
                'facility_name': facility_name,
                'total_processed': total_processed,
                'duration': duration_formatted,
                'status': status,
                'message': message or f'Inventory sync completed successfully',
                'timestamp': timezone.now().isoformat()
            }
        )
        logger.info(f"Sent completion update for task {task_id}")
        
    except Exception as e:
        logger.error(f"Failed to send completion update: {str(e)}")


def send_error_update(task_id, facility_name, error_message):
    """Send error update via WebSocket channel."""
    try:
        channel_layer = get_channel_layer()
        if not channel_layer:
            logger.warning("Channel layer not configured")
            return
            
        async_to_sync(channel_layer.group_send)(
            f'inventory_sync_{task_id}',
            {
                'type': 'sync_error',
                'task_id': task_id,
                'facility_name': facility_name,
                'error_message': error_message,
                'status': 'failed',
                'timestamp': timezone.now().isoformat()
            }
        )
        logger.info(f"Sent error update for task {task_id}")
        
    except Exception as e:
        logger.error(f"Failed to send error update: {str(e)}")


def format_duration(seconds):
    """Format duration in seconds to human readable format."""
    if seconds < 60:
        return f"{int(seconds)}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}m {secs}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"


@celery_app.task(bind=True)
def sync_inventory_task(self, facility_name, start_page=1):
    """
    Celery task to synchronize inventory data from WMS systems.
    This task will be executed by Celery workers.
    
    Args:
        facility_name: Name of specific Facility to process. If None, processes all active syncs.
        start_page: Page number to start syncing from (default: 1).
    """
    task_id = self.request.id
    start_time = time.time()
    logger.info(f"Starting inventory sync task {task_id} with facility_name: {facility_name}")
    force_sync = False

    try:
        facility = Facility.objects.get(name=facility_name)
        facility_inventory_sync = FacilityInventorySync.objects.get(facility=facility)
        force_sync = facility_inventory_sync.force_sync
    except Facility.DoesNotExist:
        error_msg = f"Facility with name {facility_name} not found"
        logger.error(error_msg)
        send_error_update(task_id, facility_name, error_msg)
        return {"status": "error", "message": error_msg}
    except FacilityInventorySync.DoesNotExist:
        error_msg = f"FacilityInventorySync for facility {facility_name} not found"
        logger.error(error_msg)
        send_error_update(task_id, facility_name, error_msg)
        return {"status": "error", "message": error_msg}


    inventory_sync_id = facility_inventory_sync.inventory_sync.id
    
    try:
        # Get inventory sync configurations to process
        if facility_inventory_sync.inventory_sync.is_active:
            # Process specific sync configuration
            sync_configs = InventorySync.objects.get(id=inventory_sync_id)

            # get the list of lamda function mapped to this sync configuration
            lambda_functions = sync_configs.target_lambda_integrations.all()
            lambda_functions_conf = []
 
            # get the details of each lamds function mapped to this sync configuration as dict name region and add to list
            for lambda_function in lambda_functions:
                each_lambda_function_conf = {
                    "function_name": lambda_function.function_name,
                    "region": lambda_function.region,
                    "aws_access_key_id": lambda_function.aws_access_key_id,
                    "aws_secret_access_key": lambda_function.aws_secret_access_key
                }
                lambda_functions_conf.append(each_lambda_function_conf)

            # get the list of wms integration mapped to this sync configuration
            wms_integrations = sync_configs.source_integration

            # get the base url from wms integration
            base_url = wms_integrations.base_url
            client_id = wms_integrations.client_id
            client_secret = wms_integrations.client_secret

            # WMSInventoryService instance
            wms_inventory_service = WMSInventoryService(base_url, client_id, client_secret, facility_name)

            # get the inventory data from wms
            inventory_summary = wms_inventory_service.get_inventory_summary()
            total_pages = inventory_summary['total_pages']
            current_page = start_page
            estimated_total_items = inventory_summary['estimated_total_items']
            total_items = inventory_summary['total_items']

            #conf
            limit_per_page = 100
            sku_name = None

            if not total_items:
                error_msg = "No inventory items found"
                logger.info(error_msg)
                send_error_update(task_id, facility_name, error_msg)
                return {"status": "error", "message": error_msg}

            # Send initial progress update
            send_progress_update(task_id, 0, total_pages, 0, total_items, facility_name, start_time)
            
            processed_items = 0
            while True:
                if total_pages and current_page > total_pages:
                    break

                try:
                    page_data = wms_inventory_service.get_inventory_page(current_page, limit_per_page, sku_name)
                    inventory_items = page_data['data']
                    page_info = page_data['page_info']

                    #Process the inventory items
                    for item in inventory_items:
                        process_inventory_item(item, lambda_functions_conf, facility_name, force_sync)
                        processed_items += 1

                    print(f"Page {current_page}/{page_info['total_pages']}: {len(inventory_items)} items")
                    logger.info(f"Page {current_page}/{page_info['total_pages']}: {len(inventory_items)} items")

                    # Send progress update after processing each page
                    send_progress_update(task_id, current_page, page_info['total_pages'], processed_items, total_items, facility_name, start_time)

                    # Check if we've reached the last page
                    if current_page >= page_info['total_pages']:
                        logger.info("Reached last page")
                        break

                    time.sleep(1)
                    current_page += 1

                except WMSAPIError as e:
                    error_msg = f"Failed to retrieve page {current_page}: {e}"
                    logger.error(error_msg)
                    send_error_update(task_id, facility_name, error_msg)
                    raise

            # Send completion update
            send_completion_update(task_id, facility_name, processed_items, start_time)
            logger.info(f"Inventory sync completed for {facility_name}. Processed {processed_items} items.")
            return {"status": "completed", "processed_items": processed_items}

    except Exception as e:
        error_msg = f"Failed to retrieve inventory: {e}"
        logger.error(error_msg)
        send_error_update(task_id, facility_name, error_msg)
        raise



def process_inventory_item(item, lambda_functions_conf, facility_name, force_sync=False):

    # transform the inventory item to lambda format
    transformed_item = transform_wms_inventory_to_lambda_format(item, facility_name, encode_data=True, force_sync=force_sync)

    # send the transformed item to lambda
    for lambda_function_conf in lambda_functions_conf:
        try:
            function_name = lambda_function_conf['function_name']
            region = lambda_function_conf['region']
            aws_access_key_id = lambda_function_conf['aws_access_key_id']
            aws_secret_access_key = lambda_function_conf['aws_secret_access_key']
            lambda_wrapper = LambdaWrapper(region, aws_access_key_id, aws_secret_access_key)
            lambda_wrapper.push_data_to_lambda_async(function_name, transformed_item)
        except Exception as e:
            logger.error(f"Failed to push data to lambda: {e}")



@celery_app.task
def test_celery_task():
    """
    Simple test task to verify Celery is working correctly.
    """
    logger.info("Test Celery task executed successfully")
    return "Celery is working correctly!"
