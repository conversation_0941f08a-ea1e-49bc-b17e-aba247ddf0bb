import json
from channels.generic.websocket import AsyncWebsocketConsumer

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('websockets')   


class InventorySyncConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time inventory sync progress updates.
    Each Celery task ID becomes a unique channel group.
    """

    async def connect(self):
        """Accept WebSocket connection and join the task-specific group."""
        self.task_id = self.scope['url_route']['kwargs']['task_id']
        self.group_name = f'inventory_sync_{self.task_id}'
        
        # Join the task-specific group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )

        await self.accept()
        logger.info(f"WebSocket connected to task group: {self.group_name}")

    async def disconnect(self, close_code):
        """Leave the task-specific group when disconnecting."""
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
        logger.info(f"WebSocket disconnected from task group: {self.group_name}")

    async def receive(self, text_data):
        """Handle incoming WebSocket messages (not used for progress updates)."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type', 'ping')
            
            if message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'message': 'Connection alive'
                }))
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received: {text_data}")

    async def sync_progress(self, event):
        """Send progress update to WebSocket client."""
        logger.info(f"Sending progress update to WebSocket: {event}")
        await self.send(text_data=json.dumps({
            'type': 'progress',
            'task_id': event['task_id'],
            'current_page': event['current_page'],
            'total_pages': event['total_pages'],
            'processed_items': event['processed_items'],
            'total_items': event['total_items'],
            'facility_name': event['facility_name'],
            'progress_percentage': event['progress_percentage'],
            'eta': event.get('eta_formatted'),
            'status': event['status'],
            'timestamp': event['timestamp']
        }))

    async def sync_complete(self, event):
        """Send completion notification to WebSocket client."""
        await self.send(text_data=json.dumps({
            'type': 'completed',
            'task_id': event['task_id'],
            'facility_name': event['facility_name'],
            'total_processed': event['total_processed'],
            'duration': event['duration'],
            'status': event['status'],
            'message': event['message'],
            'timestamp': event['timestamp']
        }))

    async def sync_error(self, event):
        """Send error notification to WebSocket client."""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'task_id': event['task_id'],
            'facility_name': event['facility_name'],
            'message': event['error_message'],
            'status': event['status'],
            'timestamp': event['timestamp']
        }))
