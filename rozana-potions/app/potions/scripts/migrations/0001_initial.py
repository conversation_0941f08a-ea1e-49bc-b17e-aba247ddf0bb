# Generated by Django 5.2.5 on 2025-08-06 14:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        ('integrations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventorySync',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Descriptive name for this sync configuration', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.facility')),
                ('source_integration', models.Foreign<PERSON>ey(help_text='WMS system to pull data from', on_delete=django.db.models.deletion.CASCADE, to='integrations.wmsintegration')),
                ('target_lambda_integrations', models.ManyToManyField(blank=True, help_text='Lambda integrations (Redis Lambda, Typesense Lambda, etc.) to push inventory data to', to='integrations.lambdaintegration')),
            ],
            options={
                'db_table': 'INVENTORY_SYNC',
            },
        ),
        migrations.CreateModel(
            name='InventorySyncLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('success', 'Success'), ('in_progress', 'In Progress'), ('failed', 'Failed')], max_length=100)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('process_start_time', models.DateTimeField(auto_now_add=True)),
                ('process_end_time', models.DateTimeField(auto_now=True)),
                ('process_duration', models.DurationField()),
                ('procesed_records', models.IntegerField()),
                ('failed_records', models.IntegerField()),
                ('inventory_sync', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scripts.inventorysync')),
            ],
            options={
                'db_table': 'INVENTORY_SYNC_LOG',
            },
        ),
    ]
