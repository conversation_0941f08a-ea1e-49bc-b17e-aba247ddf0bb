# Generated by Django 5.2.5 on 2025-08-06 17:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_facility_name'),
        ('scripts', '0002_alter_inventorysync_unique_together'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='inventorysync',
            name='facility',
        ),
        migrations.CreateModel(
            name='FacilityInventorySync',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.facility')),
                ('inventory_sync', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='scripts.inventorysync')),
            ],
            options={
                'db_table': 'FACILITY_INVENTORY_SYNC',
                'unique_together': {('facility', 'inventory_sync')},
            },
        ),
    ]
