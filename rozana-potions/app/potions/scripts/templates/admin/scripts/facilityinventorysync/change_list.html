{% extends "admin/change_list.html" %}

{% block extrahead %}
{{ block.super }}
<style>
    .sync-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-right: 5px;
    }
    
    .sync-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }
    
    .sync-button:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    
    .progress-container {
        display: none;
        margin-top: 8px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #667eea;
    }
    
    .progress-bar-container {
        width: 100%;
        height: 6px;
        background: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 8px;
        position: relative;
    }
    
    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 3px;
        transition: width 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-image: linear-gradient(
            -45deg,
            rgba(255, 255, 255, .2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, .2) 50%,
            rgba(255, 255, 255, .2) 75%,
            transparent 75%,
            transparent
        );
        background-size: 20px 20px;
        animation: move 1s linear infinite;
    }
    
    @keyframes move {
        0% { background-position: 0 0; }
        100% { background-position: 20px 20px; }
    }
    
    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: #6c757d;
    }
    
    .progress-pages {
        font-weight: 600;
        color: #495057;
    }
    
    .progress-eta {
        font-style: italic;
    }
    
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-idle { background: #6c757d; }
    .status-connecting { background: #ffc107; animation: pulse 1s infinite; }
    .status-syncing { background: #28a745; animation: pulse 1s infinite; }
    .status-completed { background: #17a2b8; }
    .status-error { background: #dc3545; }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
    
    .sync-actions {
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block result_list %}
{{ block.super }}
<script>
// Add sync buttons and progress bars after the default table is rendered
document.addEventListener('DOMContentLoaded', function() {
    const resultTable = document.getElementById('result_list');
    if (!resultTable) return;
    
    // Add Actions column header
    const headerRow = resultTable.querySelector('thead tr');
    if (headerRow) {
        const actionsHeader = document.createElement('th');
        actionsHeader.innerHTML = '<div class="text"><span>Actions</span></div>';
        headerRow.appendChild(actionsHeader);
    }
    
    // Add sync buttons to each row - use more specific selector
    const rows = document.querySelectorAll('#result_list tbody tr');
    console.log('Debug - Number of data rows found:', rows.length);
    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length === 0) return;
        
        // Get FacilityInventorySync ID from the row checkbox
        const facilityInventorySyncId = row.querySelector('input[name="_selected_action"]')?.value;
        
        if (!facilityInventorySyncId) {
            console.warn('Missing facilityInventorySyncId for row:', index);
            return;
        }
        
        console.log('Debug - FacilityInventorySync ID found:', facilityInventorySyncId);
        
        // Create actions cell
        const actionsCell = document.createElement('td');
        actionsCell.className = 'sync-actions';
        
        // Create button element - no need for facility name, use ID only
        const syncButton = document.createElement('button');
        syncButton.className = 'sync-button';
        syncButton.textContent = 'Start Sync';
        syncButton.setAttribute('data-facilityinventorysync-id', facilityInventorySyncId);
        syncButton.onclick = function() { startSync(facilityInventorySyncId, this); };
        
        actionsCell.appendChild(syncButton);
        
        // Create progress container separately to avoid overwriting the button
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        progressContainer.id = `progress-${facilityInventorySyncId}`;
        progressContainer.style.display = 'none';
        progressContainer.innerHTML = `
            <div class="progress-bar-container">
                <div class="progress-bar" id="progress-bar-${facilityInventorySyncId}" style="width: 0%"></div>
            </div>
            <div class="progress-info">
                <span class="progress-pages">
                    <span class="status-indicator status-idle" id="status-${facilityInventorySyncId}"></span>
                    <span id="pages-${facilityInventorySyncId}">0 / 0 pages</span>
                </span>
                <span class="progress-eta" id="eta-${facilityInventorySyncId}">ETA: --</span>
            </div>
        `;
        actionsCell.appendChild(progressContainer);
        
        console.log('Created progress container with ID:', `progress-${facilityInventorySyncId}`);
        console.log('Progress container element:', progressContainer);
        
        row.appendChild(actionsCell);
    });
});
</script>

<script>
// Consolidated JavaScript for inventory sync functionality
let activeSyncs = new Map();

function startSync(facilityInventorySyncId, button) {
    console.log('Starting sync for FacilityInventorySync ID:', facilityInventorySyncId);
    
    // Disable button and show loading state
    button.disabled = true;
    button.textContent = 'Syncing...';
    
    // Get CSRF token from the page
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    // Make API call to start sync with relative URL
    fetch('/scripts/api/start-inventory-sync/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        credentials: 'same-origin',
        body: JSON.stringify({
            facilityinventorysync_id: facilityInventorySyncId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            connectWebSocket(facilityInventorySyncId, data.task_id, data.websocket_url, button);
        } else {
            showError(facilityInventorySyncId, data.error || 'Failed to start sync', button);
        }
    })
    .catch(error => {
        console.error('Error starting sync:', error);
        showError(facilityInventorySyncId, 'Network error occurred', button);
    });
}

function connectWebSocket(facilityInventorySyncId, taskId, websocketUrl, button) {
    const statusIndicator = document.getElementById(`status-${facilityInventorySyncId}`);
    const progressBar = document.getElementById(`progress-bar-${facilityInventorySyncId}`);
    const pagesSpan = document.getElementById(`pages-${facilityInventorySyncId}`);
    const etaSpan = document.getElementById(`eta-${facilityInventorySyncId}`);
    
    const socket = new WebSocket(websocketUrl);
    activeSyncs.set(facilityInventorySyncId, { socket, taskId });
    
    socket.onopen = function(event) {
        console.log('WebSocket connected for FacilityInventorySync:', facilityInventorySyncId);
        statusIndicator.className = 'status-indicator status-syncing';
        button.textContent = 'Syncing...';
        
        // Show progress container when WebSocket connects
        const progressContainer = document.getElementById(`progress-${facilityInventorySyncId}`);
        console.log('Progress container found:', progressContainer);
        if (progressContainer) {
            progressContainer.style.display = 'block';
            console.log('Progress container displayed');
        } else {
            console.error('Progress container not found with ID:', `progress-${facilityInventorySyncId}`);
        }
    };
    
    socket.onmessage = function(event) {
        try {
            console.log('=== WebSocket Message Received ===');
            const data = JSON.parse(event.data);
            console.log('Parsed data:', data);
            console.log('Message type:', data.type);
            console.log('FacilityInventorySync ID:', facilityInventorySyncId);
            
            // Test if updateProgress function exists
            console.log('updateProgress function exists:', typeof updateProgress);
            
            // Force call updateProgress for any message to test
            console.log('About to call updateProgress...');
            updateProgress(facilityInventorySyncId, data);
            console.log('updateProgress call completed');
            
            if (data.type === 'progress') {
                console.log('Progress message handled');
            } else if (data.type === 'completed') {
                console.log('Calling handleCompletion');
                handleCompletion(facilityInventorySyncId, data, button);
            } else if (data.type === 'error') {
                console.log('Calling handleError');
                handleError(facilityInventorySyncId, data, button);
            } else {
                console.log('Unknown message type:', data.type);
            }
        } catch (error) {
            console.error('Error in onmessage handler:', error);
        }
    };
    
    socket.onclose = function(event) {
        console.log('WebSocket closed for FacilityInventorySync:', facilityInventorySyncId);
        activeSyncs.delete(facilityInventorySyncId);
        
        if (event.code !== 1000) { // Not a normal closure
            showError(facilityInventorySyncId, 'Connection lost', button);
        }
    };
    
    socket.onerror = function(error) {
        console.error('WebSocket error for FacilityInventorySync:', facilityInventorySyncId, error);
        showError(facilityInventorySyncId, 'Connection error', button);
    };
}

function updateProgress(facilityInventorySyncId, data) {
    console.log('updateProgress called with ID:', facilityInventorySyncId, 'data:', data);
    
    const progressContainer = document.getElementById(`progress-${facilityInventorySyncId}`);
    const progressBar = document.getElementById(`progress-bar-${facilityInventorySyncId}`);
    const pagesSpan = document.getElementById(`pages-${facilityInventorySyncId}`);
    const etaSpan = document.getElementById(`eta-${facilityInventorySyncId}`);
    
    console.log('Progress elements found:', {
        progressContainer: progressContainer,
        progressBar: progressBar,
        pagesSpan: pagesSpan,
        etaSpan: etaSpan
    });
    
    // Show progress container if hidden
    if (progressContainer) {
        progressContainer.style.display = 'block';
    }
    
    if (!progressBar || !pagesSpan || !etaSpan) {
        console.error('Missing progress elements for ID:', facilityInventorySyncId);
        return;
    }
    
    const percentage = data.progress_percentage || 0;
    progressBar.style.width = `${percentage}%`;
    
    pagesSpan.textContent = `${data.current_page} / ${data.total_pages} pages`;
    etaSpan.textContent = `ETA: ${data.eta || '--'}`;
    
    console.log('Progress updated:', percentage + '%', `${data.current_page}/${data.total_pages}`);
}

function handleCompletion(facilityInventorySyncId, data, button) {
    const statusIndicator = document.getElementById(`status-${facilityInventorySyncId}`);
    const progressBar = document.getElementById(`progress-bar-${facilityInventorySyncId}`);
    const pagesSpan = document.getElementById(`pages-${facilityInventorySyncId}`);
    const etaSpan = document.getElementById(`eta-${facilityInventorySyncId}`);
    
    statusIndicator.className = 'status-indicator status-completed';
    progressBar.style.width = '100%';
    pagesSpan.textContent = `Completed - ${data.total_processed} items processed`;
    etaSpan.textContent = `Duration: ${data.duration}`;
    
    button.disabled = false;
    button.textContent = 'Start Sync';
    
    // Hide progress after 10 seconds
    setTimeout(() => {
        const progressContainer = document.getElementById(`progress-${facilityInventorySyncId}`);
        progressContainer.style.display = 'none';
        statusIndicator.className = 'status-indicator status-idle';
    }, 10000);
}

function handleError(facilityInventorySyncId, data, button) {
    showError(facilityInventorySyncId, data.message || data.error_message || 'Sync failed', button);
}

function showError(facilityInventorySyncId, message, button) {
    const statusIndicator = document.getElementById(`status-${facilityInventorySyncId}`);
    const pagesSpan = document.getElementById(`pages-${facilityInventorySyncId}`);
    const etaSpan = document.getElementById(`eta-${facilityInventorySyncId}`);
    
    statusIndicator.className = 'status-indicator status-error';
    pagesSpan.textContent = 'Error occurred';
    etaSpan.textContent = message;
    
    button.disabled = false;
    button.textContent = 'Start Sync';
    
    // Hide progress after 10 seconds
    setTimeout(() => {
        const progressContainer = document.getElementById(`progress-${facilityInventorySyncId}`);
        progressContainer.style.display = 'none';
        statusIndicator.className = 'status-indicator status-idle';
    }, 10000);
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    activeSyncs.forEach((sync, facilityInventorySyncId) => {
        if (sync.socket) {
            sync.socket.close();
        }
    });
});
</script>
{% endblock %}
