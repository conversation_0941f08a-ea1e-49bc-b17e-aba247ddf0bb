from django.http import JsonResponse
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views import View
from django.conf import settings
import json
from celery import current_app

from core.models import Facility
from scripts.models import FacilityInventorySync
from scripts.tasks.inventory import sync_inventory_task

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('scripts_api')


APPLICATION_PROTOCOL = getattr(settings, "APPLICATION_PROTOCOL", "http")


@method_decorator(csrf_exempt, name='dispatch')
@method_decorator(staff_member_required, name='dispatch')
class StartInventorySyncView(View):
    """
    API endpoint to start inventory sync and return WebSocket connection details.
    """
    def post(self, request):
        """Start inventory sync for a specific FacilityInventorySync."""
        try:
            data = json.loads(request.body)
            facilityinventorysync_id = data.get('facilityinventorysync_id')
            
            if not facilityinventorysync_id:
                return JsonResponse({
                    'error': 'facilityinventorysync_id is required'
                }, status=400)

            # Get FacilityInventorySync by ID
            try:
                facility_inventory_sync = FacilityInventorySync.objects.get(
                    id=facilityinventorysync_id,
                    is_active=True
                )
                facility = facility_inventory_sync.facility
            except FacilityInventorySync.DoesNotExist:
                return JsonResponse({
                    'error': f'FacilityInventorySync with ID {facilityinventorysync_id} not found or inactive'
                }, status=404)

            # Start the Celery task
            task = sync_inventory_task.apply_async(args=[facility.name])

            # Construct WebSocket URL
            if APPLICATION_PROTOCOL == 'https':
                protocol = 'wss'
            else:
                protocol = 'ws'
            host = request.get_host()
            websocket_url = f"{protocol}://{host}/ws/inventory-sync/{task.id}/"

            logger.info(f"Started inventory sync task {task.id} for facility {facility.name}")

            return JsonResponse({
                'success': True,
                'task_id': task.id,
                'facility_name': facility.name,
                'facility_id': facility.id,
                'websocket_url': websocket_url,
                'status': 'started',
                'message': f'Inventory sync started for {facility.name}'
            })

        except Exception as e:
            logger.error(f"Error starting inventory sync: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'Failed to start inventory sync: {str(e)}'
            }, status=500)

