from django.core.management.base import BaseCommand
from django_celery_beat.models import PeriodicTask, IntervalSchedule
from scripts.models import InventorySync
import json


class Command(BaseCommand):
    help = 'Set up periodic tasks for inventory synchronization'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='Interval in minutes for inventory sync (default: 60)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing periodic tasks before creating new ones'
        )

    def handle(self, *args, **options):
        interval_minutes = options['interval']
        
        if options['clear']:
            self.stdout.write('Clearing existing inventory sync periodic tasks...')
            PeriodicTask.objects.filter(name__startswith='inventory-sync-').delete()
            self.stdout.write(self.style.SUCCESS('Cleared existing tasks'))

        # Create or get interval schedule
        schedule, created = IntervalSchedule.objects.get_or_create(
            every=interval_minutes,
            period=IntervalSchedule.MINUTES,
        )
        
        if created:
            self.stdout.write(f'Created new interval schedule: every {interval_minutes} minutes')
        else:
            self.stdout.write(f'Using existing interval schedule: every {interval_minutes} minutes')

        # Get all active inventory sync configurations
        active_syncs = InventorySync.objects.filter(is_active=True)
        
        if not active_syncs.exists():
            self.stdout.write(self.style.WARNING('No active inventory sync configurations found'))
            return

        created_tasks = 0
        for sync_config in active_syncs:
            task_name = f'inventory-sync-{sync_config.name}'
            
            # Check if task already exists
            if PeriodicTask.objects.filter(name=task_name).exists():
                self.stdout.write(f'Task "{task_name}" already exists, skipping...')
                continue

            # Create periodic task
            periodic_task = PeriodicTask.objects.create(
                interval=schedule,
                name=task_name,
                task='scripts.tasks.inventory.sync_inventory_task',
                args=json.dumps([sync_config.id]),
                description=f'Periodic inventory sync for {sync_config.name}',
                enabled=True,
            )
            
            created_tasks += 1
            self.stdout.write(
                self.style.SUCCESS(
                    f'Created periodic task: {task_name} (ID: {periodic_task.id})'
                )
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_tasks} periodic tasks'
            )
        )
        
        if created_tasks > 0:
            self.stdout.write('')
            self.stdout.write('To start the Celery Beat scheduler, run:')
            self.stdout.write('  celery -A potions beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler')
