from django.db import models

class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class Facility(BaseModel):
    name = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    wms_integration_enabled = models.BooleanField(default=False, help_text="Enable WMS order creation for this facility. Use with caution in production.")

    class Meta:
        db_table = 'FACILITY'
        verbose_name = 'Facility'
        verbose_name_plural = 'Facilities'

    def __str__(self):
        return self.name

    def can_create_wms_orders(self) -> bool:
        """Check if WMS order creation is allowed for this facility"""
        return self.is_active and self.wms_integration_enabled


class HubMaster(BaseModel):
    """
    Model to store hub information for TMS integration.

    This model tracks which hubs are allowed for consignment creation
    and whether they are production or test hubs.
    """

    hub_code = models.Char<PERSON>ield(max_length=20, unique=True, help_text="Unique hub code used in TMS API")
    hub_name = models.Char<PERSON>ield(max_length=100, help_text="Human-readable hub name")
    is_prod = models.BooleanField(default=False, help_text="True if this is a production hub, False for test hub")
    is_active = models.BooleanField(default=True, help_text="Whether this hub is currently active for consignment creation")

    class Meta:
        db_table = 'TMS_HUB_MASTER'
        verbose_name = 'Hub Master'
        verbose_name_plural = 'Hub Masters'
        ordering = ['hub_code']

    def __str__(self):
        hub_type = "PROD" if self.is_prod else "TEST"
        status = "ACTIVE" if self.is_active else "INACTIVE"
        return f"{self.hub_code} ({hub_type}) - {status}"

    @classmethod
    def is_hub_allowed(cls, hub_code: str, environment: str = None) -> tuple[bool, str]:
        """
        Check if a hub is allowed for consignment creation based on environment.

        Args:
            hub_code: The hub code to validate
            environment: Environment type ('UAT' or 'PRODUCTION')

        Returns:
            tuple: (is_allowed: bool, error_message: str)
        """
        try:
            hub = cls.objects.get(hub_code=hub_code, is_active=True)
        except cls.DoesNotExist:
            return False, f"Hub '{hub_code}' is not registered or inactive"

        # If environment is UAT, only allow test hubs (is_prod=False)
        if environment == 'UAT' and hub.is_prod:
            return False, f"Hub '{hub_code}' is a production hub and cannot be used in UAT environment"

        # Production environment allows both test and prod hubs
        return True, ""


class FacilityHubMapping(BaseModel):
    """
    Model to map facility names from OMS to TMS hub codes.
    
    This allows mapping between OMS facility names (e.g., ROZANA_TEST_WH1) 
    and actual TMS hub codes (e.g., ONDEMAND_TEST_GGN).
    """
    
    facility = models.ForeignKey(Facility, on_delete=models.CASCADE, help_text="Facility from OMS")
    hub = models.ForeignKey(HubMaster, on_delete=models.CASCADE, help_text="TMS Hub")
    is_active = models.BooleanField(default=True, help_text="Whether this mapping is currently active")
    
    class Meta:
        db_table = 'FACILITY_HUB_MAPPING'
        verbose_name = 'Facility Hub Mapping'
        verbose_name_plural = 'Facility Hub Mappings'
        unique_together = ('facility', 'hub')
        ordering = ['facility__name']
    
    def __str__(self):
        return f"{self.facility.name} → {self.hub.hub_code}"
