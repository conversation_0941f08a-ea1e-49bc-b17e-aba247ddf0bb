# Generated by Django 5.2.5 on 2025-08-10 04:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_facility_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='HubMaster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hub_code', models.CharField(help_text='Unique hub code used in TMS API', max_length=20, unique=True)),
                ('hub_name', models.Char<PERSON>ield(help_text='Human-readable hub name', max_length=100)),
                ('is_prod', models.BooleanField(default=False, help_text='True if this is a production hub, False for test hub')),
                ('is_active', models.<PERSON><PERSON>anField(default=True, help_text='Whether this hub is currently active for consignment creation')),
            ],
            options={
                'verbose_name': 'Hub Master',
                'verbose_name_plural': 'Hub Masters',
                'db_table': 'TMS_HUB_MASTER',
                'ordering': ['hub_code'],
            },
        ),
    ]
