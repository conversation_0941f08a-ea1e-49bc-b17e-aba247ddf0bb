# Generated by Django 5.2.5 on 2025-08-26 11:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_add_wms_integration_enabled_to_facility'),
    ]

    operations = [
        migrations.CreateModel(
            name='FacilityHubMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this mapping is currently active')),
                ('facility', models.ForeignKey(help_text='Facility from OMS', on_delete=django.db.models.deletion.CASCADE, to='core.facility')),
                ('hub', models.ForeignKey(help_text='TMS Hub', on_delete=django.db.models.deletion.CASCADE, to='core.hubmaster')),
            ],
            options={
                'verbose_name': 'Facility Hub Mapping',
                'verbose_name_plural': 'Facility Hub Mappings',
                'db_table': 'FACILITY_HUB_MAPPING',
                'ordering': ['facility__name'],
                'unique_together': {('facility', 'hub')},
            },
        ),
    ]
