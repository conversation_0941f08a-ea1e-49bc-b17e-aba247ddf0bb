from django.contrib import admin
from core.models import Facility, HubMaster, FacilityHubMapping

@admin.register(Facility)
class FacilityAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

@admin.register(HubMaster)
class HubMasterAdmin(admin.ModelAdmin):
    """
    Admin interface for HubMaster model.
    """
    list_display = ['hub_code', 'hub_name', 'is_prod', 'is_active', 'created_at']
    list_filter = ['is_prod', 'is_active', 'created_at']
    search_fields = ['hub_code', 'hub_name']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['hub_code']

    fieldsets = (
        ('Hub Information', {
            'fields': ('hub_code', 'hub_name')
        }),
        ('Configuration', {
            'fields': ('is_prod', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(FacilityHubMapping)
class FacilityHubMappingAdmin(admin.ModelAdmin):
    list_display = ['facility', 'hub', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['facility__name', 'hub__hub_code']
    readonly_fields = ['created_at', 'updated_at']