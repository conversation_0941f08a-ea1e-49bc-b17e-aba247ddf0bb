from django.contrib import admin
from events.models import EventRoute


@admin.register(EventRoute)
class EventRouteAdmin(admin.ModelAdmin):
    list_display = ("facility__name", "event_type", "event_format", "async_enabled", "enabled", "updated_at")
    list_filter = ("facility__name", "enabled", "async_enabled")
    search_fields = ("facility__name", "event_type", "event_format")

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related("facility")
