from typing import Any, Dict, Optional
from core.models import Facility
from events.models import EventRoute
from events.tasks import process_event

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger("events_publisher")


def publish_event(event_type: str, event_format: str, payload: Dict[str, Any], facility_name: str, metadata: Optional[Dict[str, Any]] = None):
    """Common facade to send an event using DB-driven routing.

    Parameters:
    - event_type: e.g., "order_created"
    - event_format: e.g., "order_created_format"
    - facility_name: Facility name to route per facility
    - payload: dict payload to pass to handler
    - metadata: optional contextual metadata
    """
    try:
        metadata = metadata or {}

        # Validate facility
        facility_obj = Facility.objects.get(name=facility_name)

        # Find route
        route_queryset = EventRoute.objects.filter(enabled=True, facility=facility_obj, event_type=event_type, event_format=event_format)
        if not route_queryset.exists():
            raise LookupError(f"No EventRoute configured for facility={facility_obj.id} {event_type} [{event_format}]")

        route_object = route_queryset.first()
        async_flag = bool(route_object.async_enabled)

        kwargs = {
            "event_type": event_type,
            "event_format": event_format,
            "payload": payload,
            "metadata": metadata,
        }

        if async_flag:
            process_event.apply_async(kwargs=kwargs)
            return {"queued": True}
        
        # Inline execution
        result = process_event(**kwargs)
        return result

    except Exception as e:
        logger.exception(f"Error publishing event: {e}")
        pass
