from typing import Callable, Dict

from events.handlers import ( handle_order_created,handle_order_invoiced,
    handle_cn_accepted, handle_cn_delivered, handle_order_delivered,
    handle_pickup_completed, handle_order_return_event,
)


# Static mapping: simple key -> function
HANDLER_REGISTRY: Dict[str, Callable] = {
    "order_created": handle_order_created,
    "order_invoiced": handle_order_invoiced,
    "cn_accepted": handle_cn_accepted,
    "cn_delivered": handle_cn_delivered,
    "order_delivered": handle_order_delivered,
    "pickup_completed": handle_pickup_completed,
    "order_return": handle_order_return_event,
}


def get_handler_function(key: str) -> Callable:
    try:
        return HANDLER_REGISTRY[key]
    except KeyError:
        raise KeyError(f"No handler registered for key: {key}")
