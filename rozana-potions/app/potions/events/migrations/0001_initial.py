# Generated by Django 5.2.5 on 2025-08-23 15:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0004_add_wms_integration_enabled_to_facility'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(db_index=True, max_length=128)),
                ('event_format', models.CharField(db_index=True, default='*', max_length=64)),
                ('async_enabled', models.<PERSON>olean<PERSON>ield(default=True)),
                ('enabled', models.BooleanField(default=True)),
                ('description', models.Char<PERSON>ield(blank=True, max_length=255)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_routes', to='core.facility')),
            ],
            options={
                'ordering': ['facility', 'event_type', 'event_format'],
                'indexes': [models.Index(fields=['facility', 'event_type', 'event_format'], name='events_even_facilit_b7f63d_idx')],
                'unique_together': {('facility', 'event_type', 'event_format')},
            },
        ),
    ]
