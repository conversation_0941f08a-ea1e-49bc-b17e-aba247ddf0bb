from typing import Any, Dict
from celery import shared_task
from events.registry import get_handler_function

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger("events.tasks")


@shared_task(autoretry_for=(Exception,), retry_backoff=True, retry_kwargs={"max_retries": 3})
def process_event(event_type: str, event_format: str, payload: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Celery task to process an event by resolving handler from event_type."""
    try:
        handler = get_handler_function(event_type)
        logger.debug(f"Processing event {event_type} [{event_format}]")
        result = handler(event_type=event_type, event_format=event_format, payload=payload, metadata=metadata)
        return {
            "success": True,
            "event_type": event_type,
            "event_format": event_format,
            "handler": event_type,
            "result": result,
        }
    except Exception as e:
        logger.error(f"Failed to process event {event_type} [{event_format}]: {str(e)}")
        raise
