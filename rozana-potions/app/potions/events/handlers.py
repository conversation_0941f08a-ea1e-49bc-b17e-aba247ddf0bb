from potions.logging.utils import get_app_logger
from django.conf import settings
from integrations.services.whatsapp.whatsapp_notifications import WhatsappNotification
logger = get_app_logger("events_handlers")


def handle_order_created(event_type: str, event_format: str, payload: dict, metadata: dict = {}):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")

    whatsapp_notification = WhatsappNotification()
    whatsapp_notification.send_whatsapp_event_notification(event_type=event_type, payload=payload,metadata=metadata)
    return {"ok": True}

def handle_order_invoiced(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_cn_accepted(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_cn_delivered(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_order_delivered(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_pickup_completed(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}
