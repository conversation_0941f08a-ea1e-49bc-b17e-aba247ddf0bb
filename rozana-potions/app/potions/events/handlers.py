from potions.logging.utils import get_app_logger
from django.conf import settings
from integrations.services.whatsapp.whatsapp_notifications import WhatsappNotification
from integrations.services.wms.wms_service import process_order_return_to_wms
from integrations.tasks.wms_orders import process_return_to_wms_task
logger = get_app_logger("events_handlers")


def handle_order_created(event_type: str, event_format: str, payload: dict, metadata: dict = {}):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")

    whatsapp_notification = WhatsappNotification()
    whatsapp_notification.send_whatsapp_event_notification(event_type=event_type, payload=payload,metadata=metadata)
    return {"ok": True}

def handle_order_invoiced(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_cn_accepted(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_cn_delivered(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_order_delivered(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_pickup_completed(event_type: str, event_format: str, payload: dict, metadata: dict):
    logger.info(f"Handling {event_type} [{event_format}] payload={payload} metadata={metadata}")
    return {"ok": True}


def handle_order_return_event(order_id: str, return_details: dict):
    """
    Handle order return event and notify WMS.

    This function is called when a return is delivered to the warehouse
    (typically from TMS webhook indicating return delivery).

    Args:
        order_id: Original order ID
        return_details: Return details containing:
            - return_reference: Unique return identifier
            - return_reason: Reason for return
            - warehouse: Target warehouse
            - items: List of returned items (optional)

    Returns:
        dict: Processing result
    """
    try:
        logger.info(f"Handling order return event for order {order_id}")

        # Validate required fields
        if not return_details.get('return_reference'):
            logger.error(f"Missing return_reference for order {order_id}")
            return {
                'success': False,
                'error': 'return_reference is required',
                'order_id': order_id
            }

        # Option 1: Process synchronously (for immediate processing)
        # result = process_order_return_to_wms(order_id, return_details)

        # Option 2: Process asynchronously (recommended for webhook handlers)
        task = process_return_to_wms_task.apply_async(
            args=[order_id, return_details],
            retry=True
        )

        logger.info(f"Queued WMS return processing task {task.id} for order {order_id}")

        return {
            'success': True,
            'order_id': order_id,
            'return_reference': return_details.get('return_reference'),
            'task_id': task.id,
            'message': 'Return processing queued for WMS notification'
        }

    except Exception as e:
        logger.error(f"Error handling order return event for order {order_id}: {str(e)}")
        return {
            'success': False,
            'order_id': order_id,
            'error': f'Return event handling failed: {str(e)}'
        }
