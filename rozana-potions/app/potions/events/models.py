from django.db import models

# model imports
from core.models import Facility


class EventRoute(models.Model):
    """Configures how to handle a specific event type/format.

    - event_type: stable identifier like "order_created"
    - event_format: e.g. "v1", "tms", "razorpay"; use "*" as wildcard
    - async_enabled: if true use Celery; else call inline
    - enabled: toggle routing on/off
    """

    facility = models.ForeignKey(Facility, on_delete=models.CASCADE, related_name="event_routes")
    event_type = models.CharField(max_length=128, db_index=True)
    event_format = models.CharField(max_length=64, default="*", db_index=True)
    async_enabled = models.BooleanField(default=True)
    enabled = models.BooleanField(default=True)
    description = models.CharField(max_length=255, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("facility", "event_type", "event_format")
        indexes = [
            models.Index(fields=["facility", "event_type", "event_format"]),
        ]
        ordering = ["facility", "event_type", "event_format"]

    def __str__(self) -> str:
        return f"{self.facility} :: {self.event_type} [{self.event_format}] (async={self.async_enabled})"
