# Database Configuration
DATABASE_NAME=potions
DATABASE_USER=postgres
DATABASE_PASSWORD=rozana^1234
DATABASE_HOST=db1
DATABASE_PORT=5432

# OMS Database Configuration
OMS_DATABASE_HOST=host.docker.internal
OMS_DATABASE_PORT=5432
OMS_DATABASE_NAME=oms_db
OMS_DATABASE_USER=user
OMS_DATABASE_PASSWORD=password

# Redis Configuration
REDIS_BROKER_URL=redis://redis:6379/0

# TMS Configuration
TMS_BASE_URL=https://app.shipsy.in
TMS_API_KEY=your_tms_api_key
TMS_WEBHOOK_TOKEN=static_webhook_token_123

# Invoice Webhook Configuration
INVOICE_WEBHOOK_TOKEN=static_webhook_token_123

# Environment Configuration
APPLICATION_ENVIRONMENT=UAT
APPLICATION_PROTOCOL=http
WEBSOCKET_HOST=

# Liink API Configuration
LIINK_BASE_URL=https://liink-backend.stockone.com
LIINK_AUTH_TOKEN=
LIINK_WORKSPACE=ROZANA
LIINK_CONNECTOR_URL=

# Celery Configuration
CELERY_RESULT_EXPIRES=3600

# AWS S3 Static Files Configuration
USE_S3=0
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_STORAGE_BUCKET_NAME=your_aws_storage_bucket_name

# Core logging settings
ASYNC_LOGGING=FALSE
BATCH_PROCESSING=FALSE
FIREHOSE_ENABLED=FALSE
AUDIT_LOGGING_ENABLED=FALSE

# Buffer settings
APP_LOGS_CAPACITY=100
AUDIT_LOGS_CAPACITY=50
AUDIT_LOGS_GET_CAPACITY=200
LOG_BUFFER_TIMEOUT=30

# Process management
LOG_PROCESSOR_POOL_SIZE=3
MAX_QUEUE_SIZE=1000

# Firehose Configuration
FIREHOSE_REGION_NAME=ap-south-1
FIREHOSE_ACCESS_KEY_ID=your_access_key_here
FIREHOSE_SECRET_ACCESS_KEY=your_secret_key_here

# Stream Names
APP_LOGS_STREAM_NAME=local-potions-app-logs-all
AUDIT_LOGS_STREAM_NAME=local-potions-audit-logs-all
AUDIT_LOGS_GET_STREAM_NAME=local-potions-audit-logs-get

# WhatsApp Gupshup Configuration
WHATSAPP_ENABLED=true
GUPSHUP_URL=https://media.smsgupshup.com/GatewayAPI/rest
GUPSHUP_USERID=userid
GUPSHUP_PASSWORD=password
GUPSHUP_TIMEOUT=30.0
